import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from "@angular/common/http";
import { environment } from "src/environments/environment";
import { Utility } from "../shared/utility/utility";

@Injectable({
    providedIn: 'root'
})

export class promotionsService {
    baseurl: any = environment.baseUrl;
    uploadPromotionImagesURL: any = "add-promotion-image";
    addPromotionURL: any = "add-promotions";
    getPromotionsURL: any = "get-all-promotions";


    constructor(private http: HttpClient, private utility: Utility) {

    }

    uploadPromotionImages(body: any) {
        const headers = this.authorizationKey();
        return this.http.post(this.baseurl + this.uploadPromotionImagesURL, body, {
            headers: headers,
            responseType: 'text',
        });
    }

    authorizationKey() {
        let currentToken: any;
        let userToken = this.utility.decryptLSDAta(localStorage.getItem("token"));
        let authorizationToken = userToken;
        if (authorizationToken) {
            currentToken = authorizationToken;
        }
        const headers: HttpHeaders = this.createHeader(currentToken);
        return headers;
    }

    private createHeader(authorizationToken: any): HttpHeaders {
        return new HttpHeaders({
            Authorization: `Bearer ${authorizationToken}`,
        });
    }

    addPromotion(body: any) {
        const headers = this.authorizationKey();
        body = this.utility.encryptString(body);
        // body = this.utility.encrypt(body);
        return this.http.post(this.baseurl + this.addPromotionURL, body, {
            headers: headers,
            responseType: 'text',
        });
    }
    getPromotions(data: any) {
        const headers = this.authorizationKey();

        // Ensure all parameters are defined with fallbacks
        const searchedValue = data.searchedValue || '';
        const unpaged = data.unpaged;
        const page = data.currentPage !== undefined ? data.currentPage : 0;
        const size = data.pageLimit !== undefined ? data.pageLimit : 20;

        // Build URL with defined parameters
        const url = `${this.baseurl}${this.getPromotionsURL}?searchedValue=${searchedValue}&unpaged=${unpaged}&page=${page}&size=${size}`;

        console.log('Calling API with URL:', url);

        return this.http.get(url, {
            headers: headers,
            responseType: 'text',
        });
    }
}
