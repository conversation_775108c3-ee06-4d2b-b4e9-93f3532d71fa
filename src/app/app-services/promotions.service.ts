import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from "@angular/common/http";
import { environment } from "src/environments/environment";
import { Utility } from "../shared/utility/utility";

@Injectable({
    providedIn: 'root'
})

export class ApproverManagementService {
    baseurl: any = environment.baseUrl;
    uploadPromotionImagesURL: any = "add-promotion-images";


    constructor(private http: HttpClient, private utility: Utility) {

    }

    uploadPromotionImages(body: any) {
        const headers = this.authorizationKey();
        return this.http.post(this.baseurl + this.uploadPromotionImagesURL, body, {
            headers: headers,
            responseType: 'text',
        });
    }

    authorizationKey() {
        let currentToken: any;
        let userToken = this.utility.decryptLSDAta(localStorage.getItem("token"));
        let authorizationToken = userToken;
        if (authorizationToken) {
            currentToken = authorizationToken;
        }
        const headers: HttpHeaders = this.createHeader(currentToken);
        return headers;
    }

    private createHeader(authorizationToken: any): HttpHeaders {
        return new HttpHeaders({
            Authorization: `Bearer ${authorizationToken}`,
        });
    }
}
