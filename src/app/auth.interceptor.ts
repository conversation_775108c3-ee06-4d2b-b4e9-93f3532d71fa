import { Injectable, inject } from '@angular/core';
import {
  HttpInterceptor,
  HttpRequest,
  HttpHandler,
  HttpEvent,
  HttpErrorResponse
} from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { Router } from '@angular/router';
import { ToastrService } from 'ngx-toastr';

@Injectable()
export class AuthInterceptor implements HttpInterceptor {
  private router = inject(Router);
  private toastr = inject(ToastrService);

  intercept(req: HttpRequest<any>, next: <PERSON>ttp<PERSON>and<PERSON>): Observable<HttpEvent<any>> {
    const excludedUrls = ['/sign-in']; // Add URLs to exclude
    const isExcluded = excludedUrls.some(url => req.url.includes(url));

    if (isExcluded) {
      return next.handle(req); // Skip interception for excluded URLs
    }

    const token = localStorage.getItem('authToken'); // Retrieve token from localStorage

    let clonedRequest = req;
    if (token) {
      clonedRequest = req.clone({
        setHeaders: {
          Authorization: `Bearer ${token}`
        }
      });
    }

    return next.handle(clonedRequest).pipe(
      catchError((error: HttpErrorResponse) => {
        console.error('HTTP Error:', error);
        switch (error.status) {
          case 401:
            this.toastr.error('Session expired. Please login again.');
            localStorage.clear();
            this.router.navigate(['']);
            break;
          case 403:
            this.toastr.error('You do not have permission to access this resource.');
            break;
          case 500:
            this.toastr.error('An unexpected error occurred. Please try again later.');
            break;
          default:
            this.toastr.error('An error occurred. Please try again.');
        }
        return throwError(() => error);
      })
    );
  }
}
