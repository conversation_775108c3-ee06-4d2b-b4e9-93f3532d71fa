import { Component, OnInit, TemplateRef, ViewChild, ViewEncapsulation } from '@angular/core';
import { FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { MatDialog, MatDialogModule, MatDialogRef } from '@angular/material/dialog';
import { PageEvent } from '@angular/material/paginator';
import { Router, RouterModule } from '@angular/router';
import { ngxCsv } from 'ngx-csv';
import { ToastrService } from 'ngx-toastr';
import { Subject, debounceTime } from 'rxjs';
import { RewardPointsService } from 'src/app/app-services/reward-points-service';
import { SidebarServiceService } from 'src/app/app-services/sidebar-service.service';
import { SupportService } from 'src/app/app-services/support.service';
import { AppConstant } from 'src/app/constants/app.constant';
import { GlobalEvents } from 'src/app/helpers/global.events';
import { Utility } from "src/app/shared/utility/utility";
import { BaThemeSpinner } from 'src/app/theme/services';
// import * as saveAs from 'file-saver';
import { CommonModule } from '@angular/common';
// import { AllSupportRoutingModule } from './all-support-routing.module';
import { MatButtonToggleModule } from '@angular/material/button-toggle';
// import { AllSupportComponent } from './all-support.component';
import { DynamicTableComponent } from 'src/app/shared/data-table/data-table.component';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { AngularMultiSelectModule } from 'angular2-multiselect-dropdown';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { MatButtonModule } from '@angular/material/button';
import { MatSelectModule } from '@angular/material/select';
import { MatInputModule } from '@angular/material/input';
import { MatIconModule } from '@angular/material/icon';
import { MatPaginatorModule } from '@angular/material/paginator';
import { CustomCasePipe } from "src/app/app-services/custom-case.pipe";
import { UserService } from 'src/app/app-services/user-service';
import { AuthenticationHelper } from 'src/app/helpers/authentication';
import { RegionsService } from 'src/app/app-services/regions-service';
import { ZonesService } from 'src/app/app-services/zones-service';
import { DashboardService } from 'src/app/app-services/dashboard.service';

interface ConfigurationSettings {
  showPagination: boolean;
  perPage: number;
  totalRecordCount: number;
  currentPage: number;
  showActionsColumn: boolean;
  actionsColumnName: string;
  productIcon: boolean;
  noDataMessage: string;
  showEdit: boolean;
  showDelete?: boolean;
  changeStatus: boolean;
}

export enum ToggleEnum {
  Option1,
  Option2,
}
@Component({
  selector: 'app-all-promotions',
  templateUrl: './promotions.html',
  styleUrls: ['./promotions.scss'],
  encapsulation: ViewEncapsulation.None, // This is crucial for production builds
  imports: [
    CommonModule,
    RouterModule,
    FormsModule,
    ReactiveFormsModule,
    MatButtonToggleModule,
    MatDatepickerModule,
    MatNativeDateModule,
    MatButtonModule,
    MatSelectModule,
    MatInputModule,
    MatIconModule,
    MatPaginatorModule,
    MatDialogModule,
    AngularMultiSelectModule,
    DynamicTableComponent,
  ]
})

export class AllPromotionsComponent implements OnInit {
   viewProductsDialogRef!: MatDialogRef<any>;
  @ViewChild('viewProductsTemplate') viewProductsTemplate!: TemplateRef<any>;
  @ViewChild('supoortDialog') supoortDialog: TemplateRef<any> | undefined;
  @ViewChild('addEditPromotionTemplate') addEditPromotionTemplate!: TemplateRef<any>;
  @ViewChild('approvePromotionTemplate') approvePromotionTemplate!: TemplateRef<any>;
  @ViewChild('rejectPromotionTemplate') rejectPromotionTemplate!: TemplateRef<any>;
  supoortDialogRef!: MatDialogRef<any>;
  ismobileViewAllBrands: boolean = false;
  formGroup: FormGroup = new FormGroup({
    comments: new FormControl('', Validators.required)
  });

  dataTable: boolean = false;
  isView: boolean = false;
  isViewFalse: boolean = false;
  newFlag: boolean = false;
  progressFlag: boolean = false;
  userData: any = [];
  tableHead: any = [];
  tableColName: any = [];
  tableData: any = [];
  exportData: any = [];
  modelChanged: Subject<string> = new Subject<string>();
  showIndex: any = { index: null };

  configurationSettings: ConfigurationSettings = {
    showPagination: true,
    perPage: AppConstant.PER_PAGE_ITEMS,
    totalRecordCount: 0,
    currentPage: 1,
    showActionsColumn: true,
    actionsColumnName: 'Action',
    productIcon: true,
    noDataMessage: 'No data found',
    // showStatus: false,
    showEdit: true,
    showDelete: true, // Enable reject functionality (reusing showDelete for reject)
    changeStatus: false,
    // showIndex: true,
  };

  supportTab: string = 'PROMOTIONS';
  selectedStatus: string = '';
  activeButton: string | undefined;
  selectedDate: string | undefined;
  selectedValue: string = '1';
  isNewTicket: boolean = true;
  isprogressTicket: boolean = false;
  model: string = '';
  searchedValue: string | undefined;
  startDate: any;
  endDate: any;

  ticketNumber: string | undefined;
  queryTitle: string | undefined;
  createdDate: Date | undefined;

  totalRecordCount = 0;
  perPage = 20;
  currentPage = 0;

  categoryDataList: any = [];
  productRows: any[] = [];
  dialogCategoryDataList: any = [];
  category: any = [];
  dialogCategory: any = [];
  statusDataList: any = [];
  status: any = [];
  ticketId: number | undefined;

  selectedCategory: string = '';
  selectedDialogCategory: string = '';

  categoryDropdownSettings = {
    text: 'Select Category',
    enableSearchFilter: true,
    classes: 'myclass customer-dd ',
    labelKey: 'name',
    enableFilterSelectAll: false,
    singleSelection: true,
    maxHeight: 200,
    disabled: false,
    autoPosition: false,
    badgeShowLimit: 1,
    showCheckbox: false,
  };

  dialogCategoryDropdownSettings = {
    text: 'Select Category',
    enableSearchFilter: true,
    classes: 'myclass customer-dd ',
    labelKey: 'name',
    enableFilterSelectAll: false,
    singleSelection: true,
    maxHeight: 200,
    disabled: false,
    autoPosition: false,
    badgeShowLimit: 1,
    showCheckbox: false,
  };

  statusDropdownSettings = {
    text: 'Select Status',
    enableSearchFilter: true,
    classes: 'myclass customer-dd ',
    labelKey: 'name',
    enableFilterSelectAll: false,
    singleSelection: true,
    maxHeight: 200,
    disabled: false,
    autoPosition: false,
    badgeShowLimit: 1,
    showCheckbox: false,
  };

  supportAttachments: any = [];
  userRole: string | null | undefined;

  // Product view popup properties
  viewProductHead: any = [];
  viewProductData: any = [];
  viewProductColName: any = [];

  previewConfigurationSettings: ConfigurationSettings = {
    showPagination: false,
    perPage: 0,
    totalRecordCount: 0,
    currentPage: 1,
    showActionsColumn: false,
    actionsColumnName: '',
    productIcon: false,
    noDataMessage: 'No products found',
    showEdit: false,
    changeStatus: false,
  };


  // Add/Edit promotion popup properties
  promotionForm !: FormGroup;
  isEditMode: boolean = false;
  selectedFileName: string = '';
  additionalProducts: any[] = [];
  addEditPromotionDialogRef!: MatDialogRef<any>;

  // Multiselect data
  selectedRegion: any[] = [];
  selectedZone: any[] = [];
  selectedProduct: any[] = [];

  regionDataList = [];

  zoneDataList: any[] = [];

  productDataList: any[] = [];
  DataList: any[] = []; // For storing full product data

  regionDropdownSettings = {
    singleSelection: true,
    enableSearchFilter: true,
    classes: "multiselect-custom",
    badgeShowLimit: 1,
    searchPlaceholderText: 'Search Region',
    maxHeight: 150,
    disabled: false,
    autoPosition: false,
    showCheckbox: false
  };

  zoneDropdownSettings = {
    singleSelection: true,
    text: "Select Zone",
    selectAllText: 'Select All',
    unSelectAllText: 'UnSelect All',
    enableSearchFilter: true,
    classes: "multiselect-custom",
    badgeShowLimit: 1,
    searchPlaceholderText: 'Search Zone',
    maxHeight: 150,
    disabled: false,
    autoPosition: false,
    showCheckbox: false
  };

  productDropdownSettings = {
    singleSelection: true,
    text: "Select Product",
    selectAllText: 'Select All',
    unSelectAllText: 'UnSelect All',
    enableSearchFilter: true,
    classes: "multiselect-custom",
    badgeShowLimit: 1,
    searchPlaceholderText: 'Search Product',
    maxHeight: 130,
    disabled: false,
    autoPosition: false,
    showCheckbox: false
  };

  // Approve/Reject promotion properties
  selectedPromotionData: any = null;
  rejectComment: string = '';
  approvePromotionDialogRef!: MatDialogRef<any>;
  rejectPromotionDialogRef!: MatDialogRef<any>;

  constructor(
    private spinner: BaThemeSpinner,
    private supportService: SupportService,
    private toastr: ToastrService,
    private router: Router,
    private events: GlobalEvents,
    private utility: Utility,
    public dialog: MatDialog,
    private userService: UserService,
    private rewardPointService: RewardPointsService,
    private sidebarService: SidebarServiceService,
    private formBuilder: FormBuilder,
    private regionsService: RegionsService,
    private zonesService: ZonesService,
    private dashboardService: DashboardService
  ) {
    this.ismobileViewAllBrands = window.innerWidth <= 1023 ? true : false;
    this.initializePromotionForm();
  }

  ngOnInit() {
    this.initializePromotionForm();
    this.getRegion(); // Load regions on component init
    this.loadAllProducts(); // Load all products independently on init
    const localRole = AuthenticationHelper.getRole()?.trim().toUpperCase();
    if (localRole) {
      this.setRoleBasedConfig(localRole);
      this.spinner.hide();
    }
    this.userService.userRole$.subscribe(apiRole => {
      const apiRoleFormatted = apiRole?.trim().toUpperCase();
      this.userRole = apiRoleFormatted;
      if (apiRoleFormatted && apiRoleFormatted !== localRole) {
        this.setRoleBasedConfig(apiRoleFormatted);
        this.spinner.hide();
      }
    });
    this.setTableHeader();
    this.modelChanged.pipe(debounceTime(500)).subscribe((model: string) => {
      this.searchedValue = model?.trim() || '';
      this.currentPage = 0;
      this.configurationSettings.currentPage = 1;
      this.setTableHeader();
    });
  }
  private setRoleBasedConfig(role: string) {
    if (role === 'VIEWER') {
      this.configurationSettings = {
        ...this.configurationSettings,
        showActionsColumn: false
      };
      setTimeout(() => {
        this.configurationSettings = { ...this.configurationSettings };
        this.tableData = [...(this.tableData || [])];
        this.setTableHeader();
      }, 100);
    }
    else {
      this.configurationSettings = {
        ...this.configurationSettings,
        showActionsColumn: true
      };
    }
  }


  toggleEnum = ToggleEnum;
  selectedState = ToggleEnum.Option1;
  onChange($event: any) {
    this.selectedState = $event.value;
  }

  onPageChange(event: PageEvent) {
    this.currentPage = event.pageIndex;
    this.perPage = event.pageSize;
    if (this.isNewTicket) {
      this.processingTickets();
    } else if (this.isprogressTicket) {
      this.progressTickets();
    }
  }
  /**
   * Method for routing to edit or add user
   * @param event
   */
  id: any;
  title: any;
  description: string | undefined;

  userDetails(event: any) {
    let data = {
      id: event.id,
      ticketNumber: event.ticketNumber,
      title: event.title,
      description: event.description,
      comment: event.comment,
    };
    this.supoortDialogRef = this.dialog.open(this.supoortDialog!, {
      width: '70%',
      disableClose: false,
      panelClass: 'confirm-dialog-container',
      data,
      hasBackdrop: true,
    });
  }


  initializePromotionForm() {
    this.promotionForm = this.formBuilder.group({
      promotionName: ['', Validators.required],
      startDate: ['', Validators.required],
      endDate: ['', Validators.required],
      distributorName: ['', Validators.required],
      points: ['', [Validators.required, Validators.min(1)]],
      quantity: ['', [Validators.required, Validators.min(1)]]
    });
  }

  // Multiselect event handlers
  onRegionSelect(item: any): void {
    this.selectedRegion = [item];
    // Load zones for the selected region
    this.getZoneByID(item.id);
    // Reset zone selection when region changes
    this.selectedZone = [];
    // Note: Product data is now independent and loaded separately
  }

  onRegionDeSelect(item: any): void {
    this.selectedRegion = [];
    this.selectedZone = [];
    this.zoneDataList = []; // Clear zone data when region is deselected
    // Note: Product data remains available as it's independent
  }

  onZoneSelect(item: any): void {
    this.selectedZone = [item];
  }

  onZoneDeSelect(item: any): void {
    this.selectedZone = [];
  }

  onProductSelect(item: any): void {
    this.selectedProduct = [item];
  }

  onProductDeSelect(item: any): void {
    this.selectedProduct = [];
  }

  onAdditionalProductSelect(item: any, index: number): void {
    this.additionalProducts[index].selectedProduct = [item];
    this.additionalProducts[index].productId = item.id;
  }

  onAdditionalProductDeSelect(item: any, index: number): void {
    this.additionalProducts[index].selectedProduct = [];
    this.additionalProducts[index].productId = null;
  }

  // API Methods for loading regions and zones
   getRegion() {
    this.regionDataList = [];
    this.userService.getRegion().subscribe(
      (response: any) => {
        response = this.utility.decryptString(response)
        let leaderArray = JSON.parse(response);
        this.regionDataList = leaderArray.map((item: any) => ({
          id: item.id,
          itemName: item.name,
          code: item.code,
        }));
      },
      (error) => {
      }
    );
  }
  
    getZoneByID(id: any) {
    const data = {
      zoneId: id
    }; // if needed, add request params here
    this.zoneDataList = [];
    this.userService.getZoneById(data).subscribe(
      (response: any) => {
        response = this.utility.decryptString(response)
        let leaderArray = JSON.parse(response);
        this.zoneDataList = leaderArray.map((item: any) => ({
          id: item.id,
          itemName: item.name,
          code: item.code,
        }));
      },
      (error) => {
        // console.error('❌ Failed to fetch redeemed method data', error);
      }
    );
  }

  getAllProductData(event: any) {
    this.spinner.show();
    this.rewardPointService.getProductByID({ id: event.id }).subscribe({
      next: (response: any) => {
        const parsedData = typeof response === 'string' ? JSON.parse(this.utility.decryptString(response)) : JSON.parse(response);

        if (Array.isArray(parsedData)) {
          this.DataList = parsedData.map((item: any) => ({
            id: item.id || item.databricks_material_cd,
            itemName: item.databricks_material_desc || item.description,
            materialCode: item.databricks_material_cd || '',
            bonusPercentage: item.bonusPercentage || 0,
            materialNumber: item.databricks_material_nbr,
            category: item.category || '',
            portfolio: item.portfolio || '',
          }));

          // Update productDataList for multiselect dropdown
          this.productDataList = this.DataList.map((item: any) => ({
            id: item.id,
            itemName: item.itemName
          }));

          this.setPreSelectedValues(); // Update preselections after data load
        }
        this.spinner.hide();
      },
      error: (error) => {
        this.toastr.error('Failed to load products');
        this.spinner.hide();
      }
    });
  }

  setPreSelectedValues() {
    // Method to handle pre-selected values if needed
    // This can be implemented based on your requirements
  }

  async openDialogForm(data: any) {
    // Open the edit promotion dialog instead of the old support dialog
    this.openEditPromotionDialog(data);
  }



  async waitForTicketInfo(data: any): Promise<void> {
    return new Promise<void>((resolve) => {
      this.setTicketInfo(data);
      resolve(); // Resolve the promise immediately since setTicketInfo doesn't return a promise
    });
  }

  setTicketInfo(ticketInfo: any) {
    this.supportAttachments = [];
    ticketInfo.supportAttachments.forEach((supportAttachment: any) => {
      let supportAttachmentObject = {
        id: supportAttachment.id,
        url: supportAttachment.url,
        fileName: supportAttachment.fileName,
        contentType: supportAttachment.contentType,
        fileSize: supportAttachment.fileSize,
        supportMediaType: supportAttachment.supportMediaType,
      };
      this.supportAttachments.push(supportAttachmentObject);
    });
    this.dialogCategory = [];
    this.status = [];
    this.dialogCategoryDataList.forEach((categoryData: any) => {
      if (categoryData.name == ticketInfo.category) {
        this.dialogCategory.push(categoryData);
        this.selectedDialogCategory = categoryData.name;
      }
    });
    this.statusDataList.forEach((statusData: any) => {
      if (statusData.status == ticketInfo.status) {
        this.status.push(statusData);
        this.selectedStatus = statusData.status;
      }
    });
    this.ticketId = ticketInfo.id;
    if (this.isNewTicket) {
      this.formGroup = this.formBuilder.group({
        ticketNumber: [ticketInfo.ticketNumber],
        queryTitle: [ticketInfo.title],
        leaderName: [ticketInfo.leaderName, Validators.required],
        createdDate: [new Date(ticketInfo.createdDate)],
        comments: [ticketInfo.comment, Validators.required],
        description: [ticketInfo.description, Validators.required],
      });
    } else if (this.isprogressTicket) {
      this.formGroup = this.formBuilder.group({
        ticketNumber: [ticketInfo.ticketNumber, Validators.required],
        queryTitle: [ticketInfo.title, Validators.required],
        leaderName: [ticketInfo.leaderName, Validators.required],
        createdDate: [new Date(ticketInfo.updatedDate), Validators.required],
        comments: [ticketInfo.comment, Validators.required],
        description: [ticketInfo.description, Validators.required],
        distributorName: [ticketInfo.distributorName, Validators.required],
        distributorCode: [ticketInfo.distributorCode, Validators.required],
      });
    } else {
      this.formGroup = this.formBuilder.group({
        ticketNumber: [ticketInfo.ticketNumber, Validators.required],
        queryTitle: [ticketInfo.title, Validators.required],
        leaderName: [ticketInfo.leaderName, Validators.required],
        createdDate: [new Date(ticketInfo.createdDate), Validators.required],
        comments: [ticketInfo.comment, Validators.required],
        description: [ticketInfo.description, Validators.required],
      });
    }
    this.formGroup.patchValue({
      comments: ''
    });
    this.formGroup.get('ticketNumber')?.disable();
    this.formGroup.get('queryTitle')?.disable();
    this.formGroup.get('description')?.disable();
  }

  /**
   * Triggered when category are selected
   * @param data
   */
  onCloseForm() {
    this.supoortDialogRef.close();
    this.formGroup.reset();
    this.selectedCategory = '';
    this.selectedDialogCategory = '';
    this.selectedStatus = '';
    this.categoryDataList = [];
    this.statusDataList = [];
    this.category = [];
    this.status = [];
    this.supportAttachments = [];
    this.formGroup.patchValue({
      comments: '',
    });
  }

  ChangeStatus(event: any) { }

  /**
   * Method for the page change event
   * @param page
   */
  getUsersPageData(page: any) {
    // this.currentPage = page;
    if (!this.newFlag) {
      this.progressTickets(this.currentPage);
    } else {
      this.processingTickets(this.currentPage);
    }
  }

  getsupportPageData(page: number): void {
    this.configurationSettings.currentPage = page;
    this.currentPage = page - 1; // Convert to 0-based index for API
    if (this.isNewTicket) {
      this.processingTickets();
    } else if (this.isprogressTicket) {
      this.progressTickets();
    }
  }

  /**
   * Method for setting the headers of the table for different customer types
   */
  setTableHeader() {
    if (this.isNewTicket) {
      this.tableHead = [
        'Promotion Name.',
        'Products',
        'Distributor Name',
        'Region',
        'Zone',
        'Start Date',
        'End Date',
        'Status',
      ];
      this.tableColName = [
        'Sr_No',
        'product',
        'ticketNumber',
        'mobileNo',
        'category',
        'title',
        'createdDate',
        'status',
      ];
      this.tableData = [];
      this.processingTickets();
    } else if (this.isprogressTicket) {
      this.tableHead = [
        'Promotion Name',
        'Leader Name',
        'Product Name',
        'Portfolio',
        'Category',
        'Quantity',
        'Bonification Amount',
        'Distributor Name',
        'Distributor Code',
      ];
      this.tableColName = [
        'Sr_No',
        'leaderName',
        'ticketNumber',
        'mobileNo',
        'category',
        'title',
        'updatedDate',
        'Distributor Name',
        'Distributor Code',

      ];
      this.tableData = [];
      this.progressTickets();
    } else {
      this.tableHead = ['Leader Name', 'Ticket No', 'Mobile No', 'Category', 'Query Title', 'Created Date', 'Status'];
      this.tableColName = ['leaderName', 'ticketNumber', 'mobileNo', 'category', 'title', 'createdDate', 'status'];
      this.tableData = [];
      this.processingTickets();
    }
  }

  onWindowResizeBrands(event: any) {
    this.ismobileViewAllBrands = window.innerWidth <= 1023 ? true : false;
  }

  /**
   * Method for getting results on the basis of search query
   * @param searchString
   */
  onSearch(event: any) {
    this.modelChanged.next(event);
  }

  /**
   * Method for clearing search query
   */
  clearSearch() {
    this.searchedValue = '';
    this.model = '';
    // Reset pagination when clearing search
    this.currentPage = 0;
    this.configurationSettings.currentPage = 1;
    this.setTableHeader();
  }

  /**
   * Method for exporting data in CSV format
   * @param event
   */
  onExport(event: any) {
    if (event) {
      this.getSupportExportData();
    }
  }


  ticketTab(tab: string) {
    this.spinner.show();
    const localRole = AuthenticationHelper.getRole()?.trim().toUpperCase();
    this.model = '';
    this.searchedValue = '';
    this.startDate = '';
    this.endDate = '';
    this.category = '';
    this.selectedCategory = '';
    // Reset pagination when switching tabs
    this.currentPage = 0;
    this.configurationSettings.currentPage = 1;

    switch (tab) {
      case 'PROMOTIONS':
        this.supportTab = 'PROMOTIONS';
        this.isNewTicket = true;
        this.isprogressTicket = false;
        if (localRole === 'VIEWER') {
          this.configurationSettings.showActionsColumn = false;
        } else {
          this.configurationSettings.showActionsColumn = true;
        }
        break;

      case 'PROGRESS':
        this.supportTab = 'PROGRESS';
        this.isNewTicket = false;
        this.isprogressTicket = true;
        this.configurationSettings.showActionsColumn = false;  // Always false for PROGRESS tab
        break;

      default:
        this.isNewTicket = true;
        this.isprogressTicket = false;
        if (localRole === 'VIEWER') {
          this.configurationSettings.showActionsColumn = false;
        } else {
          this.configurationSettings.showActionsColumn = true;
        }
        break;
    }
    this.configurationSettings = { ...this.configurationSettings };

    this.setTableHeader();
    this.spinner.hide();
  }
    viewProduct(event: any) {
    this.openViewProduct(event);
  }

  openViewProduct(event: any) {
    this.spinner.show(); // Show spinner before opening dialog

    // Ensure products are loaded before setting up view data
    if (this.DataList && this.DataList.length > 0) {
      this.setupProductViewData();
      this.openProductViewDialog();
    } else {
      // Load products first, then setup view data
      this.loadAllProducts();
      // Wait a moment for the API call to complete, then setup view data
      setTimeout(() => {
        this.setupProductViewData();
        this.openProductViewDialog();
      }, 1000);
    }
  }

  private openProductViewDialog() {
    this.viewProductsDialogRef = this.dialog.open(this.viewProductsTemplate, {
      width: '60%',
      disableClose: false,
      panelClass: 'custom-popup',
      hasBackdrop: true,
      autoFocus: false
    });

    this.viewProductsDialogRef.afterOpened().subscribe(() => {
      this.spinner.hide(); // Hide spinner after dialog opens
    });

    this.viewProductsDialogRef.backdropClick().subscribe(() => {
      this.viewProductsDialogRef.close();
    });

    this.viewProductsDialogRef.afterClosed().subscribe(() => {
      // Clean up data when dialog closes
      this.viewProductData = [];
    });
  }

  setupProductViewData() {
    // Set up table headers for product view
    this.viewProductHead = [
      'Product Name',
      'UPL Code',
      'Portfolio',
      'Category',
      'Target Quantity',
    ];

    this.viewProductColName = [
      'productName',
      'quantity',
      'portfolio',
      'totalCost',
      'segment',
    ];

    // Use real product data from DataList if available, otherwise empty array
    if (this.DataList && this.DataList.length > 0) {
      this.viewProductData = this.DataList.map((item: any) => ({
        productName: item.itemName,
        quantity: '0', // Default quantity since this is view data
        unitCost: '$0.00', // Default cost
        totalCost: '$0.00', // Default total cost
        segment: item.category || 'N/A',
        portfolio: item.portfolio || 'N/A'
      }));
    } else {
      // If no data available, show empty array
      this.viewProductData = [];
    }

    this.previewConfigurationSettings.totalRecordCount = this.viewProductData.length;
  }


  closePopup() {
    if (this.viewProductsDialogRef) {
      this.viewProductsDialogRef.close();
    }
  }

  getPageData(page: number) {
    this.previewConfigurationSettings.currentPage = page;
    // In a real application, you would fetch data for the specific page
    // For now, we're using static dummy data
  }

  // Add/Edit Promotion Methods
  openAddPromotionDialog() {
    this.isEditMode = false;
    this.promotionForm.reset();
    this.selectedFileName = '';

    // Initialize productRows with at least one row
    this.productRows = [{
      selectedProduct: [],
      quantity: null,
      productId: null
    }];

    // Load all products independently (not dependent on region/zone)
    this.loadAllProducts();

    this.addEditPromotionDialogRef = this.dialog.open(this.addEditPromotionTemplate, {
      width: '60%',
      maxWidth: '600px',
      disableClose: true,
      panelClass: 'custom-popup',
      hasBackdrop: true,
      autoFocus: false
    });

    this.addEditPromotionDialogRef.afterClosed().subscribe(() => {
      // Clean up when dialog closes
      this.resetPromotionDialog();
    });
  }

  

  openEditPromotionDialog(data: any) {
    this.isEditMode = true;
    this.populateFormForEdit(data);

    // Initialize productRows with at least one row for edit mode
    this.productRows = [{
      selectedProduct: [],
      quantity: data.quantity || null,
      productId: data.productId || null
    }];

    // Load all products independently
    this.loadAllProducts();

    this.addEditPromotionDialogRef = this.dialog.open(this.addEditPromotionTemplate, {
      width: '70%',
      maxWidth: '800px',
      disableClose: true,
      panelClass: 'custom-popup',
      hasBackdrop: true,
      autoFocus: false
    });

    this.addEditPromotionDialogRef.afterClosed().subscribe(() => {
      // Clean up when dialog closes
      this.resetPromotionDialog();
    });
  }

  populateFormForEdit(data: any) {
    this.promotionForm.patchValue({
      promotionName: data.promotionName || '',
      startDate: data.startDate || '',
      endDate: data.endDate || '',
      region: data.region || '',
      zone: data.zone || '',
      distributorName: data.distributorName || '',
      points: data.points || '',
      productName: data.productName || '',
      quantity: data.quantity || ''
    });
  }

  onFileSelect(event: any) {
    const file = event.target.files[0];
    if (file) {
      this.selectedFileName = file.name;
      // Handle file upload logic here
    }
  }



  canAddMore(): boolean {
    // Check if the first product row has both product selected and quantity filled
    if (this.productRows.length > 0) {
      const firstRow = this.productRows[0];
      return !!(firstRow.selectedProduct.length && firstRow.quantity);
    }
    return false;
  }

  addMoreProduct(): void {
    if (this.canAddMore()) {
      this.additionalProducts.push({
        selectedProduct: [],
        quantity: null,
        productId: null
      });
    }
  }

  addProductRow(): void {
    // Add more product row functionality
    this.addMoreProductRow();
  }



  onSubmitPromotion(): void {
    if (this.promotionForm.valid) {
      const formData = {
        ...this.promotionForm.value,
        region: this.selectedRegion[0]?.id,
        zone: this.selectedZone[0]?.id,
        productName: this.selectedProduct[0]?.id,
        additionalProducts: this.additionalProducts.map(p => ({
          productId: p.selectedProduct[0]?.id,
          quantity: p.quantity
        }))
      };
      console.log('Form submitted:', formData);

      // Here you would typically call an API to save the promotion
      // For now, just close the dialog
      this.closePromotionPopup();

      // Show success message
      this.toastr.success(this.isEditMode ? 'Promotion updated successfully!' : 'Promotion added successfully!');
    } else {
      // Mark all fields as touched to show validation errors
      Object.keys(this.promotionForm.controls).forEach(key => {
        this.promotionForm.get(key)?.markAsTouched();
      });
    }
  }

  closePromotionPopup() {
    // Clear all form data when popup is closed
    this.clearFormData();
    if (this.addEditPromotionDialogRef) {
      this.addEditPromotionDialogRef.close();
    }
  }

  clearFormData(): void {
    // Reset form
    this.promotionForm.reset();

    // Clear multiselect data
    this.selectedRegion = [];
    this.selectedZone = [];
    this.selectedProduct = [];

    // Clear additional products and product rows
    this.additionalProducts = [];
    this.productRows = [];

    // Clear zone data list
    this.zoneDataList = [];

    // Clear product data lists
    this.productDataList = [];
    this.DataList = [];

    // Clear file selection
    this.selectedFileName = '';

    // Reset edit mode
    this.isEditMode = false;
  }


  processingTickets(page?: any) {
    const VIEW_ICON_PATH = 'assets/img/product.svg';
    this.supportService.setButtonState('new');
    this.spinner.show();
    let data = {
      currentPage: this.currentPage ? this.currentPage : 0,
      pageSize: this.perPage,
      searchedValue: this.searchedValue ? this.searchedValue : '',
      category: this.selectedCategory ? this.selectedCategory : '',
      startDate: this.startDate ? this.startDate : '',
      endDate: this.endDate ? this.endDate : '',
      sort: 'updatedDate,desc',
      isProcessing: true,
    };
    this.supportService.getProcessingTickets(data).subscribe({
      next: (processingTicket: any) => {
        try {
          // First, check if response is a string and parse it if needed
          let parsedResponse = processingTicket;
          if (typeof processingTicket === 'string') {
            parsedResponse = JSON.parse(processingTicket);
          }

          // Check if response has encryptedBody property
          if (parsedResponse && parsedResponse.encryptedBody) {
            // Decrypt the encryptedBody
            const decrypted = this.utility.decrypt(parsedResponse.encryptedBody);
            parsedResponse = JSON.parse(decrypted);
          } else {
            // Try to decrypt the entire response
            const decrypted = this.utility.decrypt(processingTicket);
            parsedResponse = JSON.parse(decrypted);
          }

          if (parsedResponse && parsedResponse.content && parsedResponse.content.length) {
            this.tableData = [];
            let i = 1;
            parsedResponse.content.forEach((processingTicketInfo: any) => {
              this.showIndex = i++;
              let processingTicketObj = {
                Sr_No: this.showIndex,
                id: processingTicketInfo.id ? processingTicketInfo.id : 'NA',
                ticketNumber: processingTicketInfo.ticketNumber
                  ? processingTicketInfo.ticketNumber
                  : 'NA',
                // leaderName: processingTicketInfo.customerName
                //   ? this.utility.toUpperCaseUtil(processingTicketInfo.customerName)
                //   : 'NA',
                product: VIEW_ICON_PATH,
                mobileNo: processingTicketInfo.mobileNo
                  ? processingTicketInfo.mobileNo
                  : 'NA',
                title: processingTicketInfo.title
                  ? processingTicketInfo.title
                  : 'NA',
                description: processingTicketInfo.description
                  ? processingTicketInfo.description
                  : 'NA',
                comment: processingTicketInfo.comment
                  ? processingTicketInfo.comment
                  : '',
                category: processingTicketInfo.category
                  ? processingTicketInfo.category
                  : 'NA',
                createdDate: processingTicketInfo.createdDate
                  ? processingTicketInfo.createdDate
                  : 'NA',
                status: processingTicketInfo.supportTicketStatus
                  ? processingTicketInfo.supportTicketStatus
                  : '',
                supportAttachments: processingTicketInfo.supportAttachments
                  ? processingTicketInfo.supportAttachments
                  : [],
              };
              this.tableData.push(processingTicketObj);
            });
            this.configurationSettings.totalRecordCount = parsedResponse.totalElements;
          } else {
            this.configurationSettings.totalRecordCount = 0;
            this.tableData = [];
            this.newFlag = false;
          }
          localStorage.setItem('newFlag', JSON.stringify(this.newFlag));
          this.events.setChangedContentTopText(
            'Promotions (' + this.configurationSettings.totalRecordCount + ')'
          );
          this.newFlag = true;
          this.spinner.hide();
        } catch (error) {
          console.error('Error parsing processing tickets:', error);
          this.configurationSettings.totalRecordCount = 0;
          this.tableData = [];
          this.spinner.hide();
        }
      },
      error: (errorResponse: any) => {
        try {
          let error = typeof errorResponse.error === 'string' ?
            JSON.parse(this.utility.decrypt(errorResponse.error)) :
            errorResponse.error;

          if (typeof error === 'string') {
            error = JSON.parse(error);
          }

          if (error && error.message) {
            this.toastr.error(error.message);
          }

          let errorMsg = errorResponse.status;
          if (+errorMsg === 401 || +errorMsg === 404) {
            localStorage.clear();
            this.router.navigate(['']);
            this.toastr.success('Signed Out Successfully');
          }
        } catch (e) {
          console.error('Error parsing error response:', e);
          this.toastr.error('An error occurred');
        }
        this.spinner.hide();
      },
    });
  }

  /**
   * API call
   * @param page
   */
  progressTickets(page?: any) {
    this.supportService.setButtonState('progress');
    this.selectedValue = '2';
    this.spinner.show();
    this.newFlag = false;
    let data = {
      currentPage: this.currentPage ? this.currentPage : 0,
      pageSize: this.perPage,
      searchedValue: this.searchedValue ? this.searchedValue : '',
      category: this.selectedCategory ? this.selectedCategory : '',
      startDate: this.startDate ? this.startDate : '',
      endDate: this.endDate ? this.endDate : '',
      sort: 'updatedDate,desc',
      isProcessing: false,
    };
    this.supportService.getResolvedTickets(data).subscribe({
      next: (res: any) => {
        try {
          // First, check if response is a string and parse it if needed
          let parsedResponse = res;
          if (typeof res === 'string') {
            parsedResponse = JSON.parse(res);
          }

          // Check if response has encryptedBody property
          if (parsedResponse && parsedResponse.encryptedBody) {
            // Decrypt the encryptedBody
            const decrypted = this.utility.decrypt(parsedResponse.encryptedBody);
            parsedResponse = JSON.parse(decrypted);
          } else {
            // Try to decrypt the entire response if needed
            if (typeof res === 'string') {
              const decrypted = this.utility.decrypt(res);
              parsedResponse = JSON.parse(decrypted);
            }
          }

          if (parsedResponse && parsedResponse.content && parsedResponse.content.length) {
            this.tableData = [];
            let i = 1;
            parsedResponse.content.forEach((progressTicketInfo: any) => {
              this.showIndex = i++;
              let data = {
                Sr_No: this.showIndex,
                id: progressTicketInfo.id ? progressTicketInfo.id : 'NA',
                ticketNumber: progressTicketInfo.ticketNumber
                  ? progressTicketInfo.ticketNumber
                  : 'NA',
                leaderName: progressTicketInfo.customerName
                  ? this.utility.toUpperCaseUtil(progressTicketInfo.customerName)
                  : 'NA',
                mobileNo: progressTicketInfo.mobileNo
                  ? progressTicketInfo.mobileNo
                  : 'NA',
                title: progressTicketInfo.title
                  ? progressTicketInfo.title
                  : 'NA',
                description: progressTicketInfo.description
                  ? progressTicketInfo.description
                  : 'NA',
                comment: progressTicketInfo.comment
                  ? progressTicketInfo.comment
                  : 'NA',
                category: progressTicketInfo.category
                  ? progressTicketInfo.category
                  : 'NA',
                updatedDate: progressTicketInfo.updatedDate
                  ? progressTicketInfo.updatedDate
                  : 'NA',
                status: progressTicketInfo.supportTicketStatus
                  ? progressTicketInfo.supportTicketStatus
                  : '',
                supportAttachments: progressTicketInfo.supportAttachments
                  ? progressTicketInfo.supportAttachments
                  : [],
                distributorName: progressTicketInfo.distributorName
                  ? progressTicketInfo.distributorName
                  : 'NA',
                distributorCode: progressTicketInfo.distributorCode
                  ? progressTicketInfo.distributorCode
                  : 'NA',
              };
              this.tableData.push(data);
            });
            this.configurationSettings.totalRecordCount = parsedResponse.totalElements;
            this.spinner.hide();
          } else {
            this.configurationSettings.totalRecordCount = 0;
            this.tableData = [];
            this.progressFlag = false;
          }
          this.events.setChangedContentTopText(
            'Promotions (' + this.configurationSettings.totalRecordCount + ')'
          );
          this.progressFlag = false;
          this.spinner.hide();
        } catch (error) {
          console.error('Error parsing progress tickets:', error);
          this.configurationSettings.totalRecordCount = 0;
          this.tableData = [];
          this.spinner.hide();
        }
      },
      error: (errorResponse: any) => {
        try {
          let error = typeof errorResponse.error === 'string' ?
            JSON.parse(this.utility.decrypt(errorResponse.error)) :
            errorResponse.error;

          if (typeof error === 'string') {
            error = JSON.parse(error);
          }

          if (error && error.message) {
            this.toastr.error(error.message);
          }

          let errorMsg = errorResponse.status;
          if (+errorMsg === 401 || +errorMsg === 404) {
            localStorage.clear();
            this.router.navigate(['']);
            this.toastr.success('Signed Out Successfully');
          }
        } catch (e) {
          console.error('Error parsing error response:', e);
          this.toastr.error('An error occurred');
        }
        this.spinner.hide();
      },
    });
  }

  /**
   * API call for exporting data in CSV format
   */
  getSupportExportData() {
    window.scrollTo(0, 0);
    this.spinner.show();
    let data = {
      status: this.supportTab == 'PROMOTIONS' ? true : false,
      currentPage: this.currentPage ? this.currentPage : 0,
      pageSize: this.perPage,
      searchedValue: this.searchedValue ? this.searchedValue : '',
      category: this.selectedCategory ? this.selectedCategory : '',
      startDate: this.startDate ? this.startDate : '',
      endDate: this.endDate ? this.endDate : '',
      sort: 'updatedDate,desc',
      unPaged: true,
    };
    const exports = this.supportService.getAllSupportExportData(data);
    exports.subscribe({
      next: (exportSupportData: any) => {
        try {
          // First, check if response is a string and parse it if needed
          let parsedResponse = exportSupportData;
          if (typeof exportSupportData === 'string') {
            parsedResponse = JSON.parse(exportSupportData);
          }

          // Check if response has encryptedBody property
          if (parsedResponse && parsedResponse.encryptedBody) {
            // Decrypt the encryptedBody
            const decrypted = this.utility.decrypt(parsedResponse.encryptedBody);
            parsedResponse = JSON.parse(decrypted);
          } else {
            // Try to decrypt the entire response if needed
            if (typeof exportSupportData === 'string') {
              const decrypted = this.utility.decrypt(exportSupportData);
              parsedResponse = JSON.parse(decrypted);
            }
          }

          this.exportData = [];
          if (parsedResponse && parsedResponse.content && parsedResponse.content.length) {
            parsedResponse.content.forEach((exportSupportInfo: any) => {
              let exportObj = {
                leaderName: exportSupportInfo.customerName
                  ? this.utility.toUpperCaseUtil(exportSupportInfo.customerName)
                  : 'NA',
                ticketNumber: exportSupportInfo.ticketNumber
                  ? exportSupportInfo.ticketNumber
                  : 'NA',
                mobileNo: exportSupportInfo.mobileNo
                  ? exportSupportInfo.mobileNo
                  : 'NA',
                title: exportSupportInfo.title ? exportSupportInfo.title : 'NA',
                description: exportSupportInfo.description
                  ? exportSupportInfo.description
                  : 'NA',
                comment: exportSupportInfo.comment
                  ? exportSupportInfo.comment
                  : 'NA',
                category: exportSupportInfo.category
                  ? exportSupportInfo.category
                  : 'NA',
              };
              this.exportData.push(exportObj);
            });
            let options = {
              fieldSeparator: ',',
              quoteStrings: '"',
              decimalseparator: '.',
              showLabels: true,
              headers: [
                'Leader Name',
                'Ticket Number',
                'Mobile No',
                'Query Title',
                'Description',
                'Comments',
                'Category',
              ],
            };
            if (this.isNewTicket) {
              new ngxCsv(this.exportData, 'New Ticket', options);
            } else {
              new ngxCsv(this.exportData, 'Progress Ticket', options);
            }
          } else {
            this.toastr.warning('No data available');
          }
          this.spinner.hide();
        } catch (error) {
          console.error('Error parsing export data:', error);
          this.toastr.warning('No data available');
          this.spinner.hide();
        }
      },
      error: (errorResponse: any) => {
        try {
          let error = typeof errorResponse.error === 'string' ?
            JSON.parse(this.utility.decrypt(errorResponse.error)) :
            errorResponse.error;

          if (typeof error === 'string') {
            error = JSON.parse(error);
          }

          if (error && error.message) {
            this.toastr.error(error.message);
          }

          let errorMsg = errorResponse.status;
          if (+errorMsg === 401 || +errorMsg === 404) {
            localStorage.clear();
            this.router.navigate(['']);
            this.toastr.success('Signed Out Successfully');
          }
        } catch (e) {
          console.error('Error parsing error response:', e);
          this.toastr.error('An error occurred');
        }
        this.spinner.hide();
      },
    });
  }



  functionForName(event: any) {
    const k = event.charCode || event.keyCode;
    const lastChar = event.target.value.slice(-1);

    if (event.target.selectionStart === 0 && event.code === 'Space') {
      event.preventDefault();
    }

    if (lastChar === ' ' && event.code === 'Space') {
      event.preventDefault();
    }

    // Allow letters (uppercase and lowercase), numbers, spaces, and specified characters
    if (
      (k >= 65 && k <= 90) ||
      (k >= 97 && k <= 122) ||
      k === 32 || // space
      (k >= 48 && k <= 57) || // numbers
      k === 8 || // backspace
      k === 95 || // underscore
      k === 45 || // hyphen
      k === 58 || // colon
      k === 46 || // period
      k === 44 || // comma
      k === 63 || // question mark
      k === 34 || // double quote
      k === 39 || // single quote
      k === 40 || // open parenthesis
      k === 41 || // close parenthesis
      k === 91 || // open square bracket
      k === 93 || // close square bracket
      k === 38 || // ampersand
      k === 42 // asterisk
    ) {
      return true;
    } else {
      event.preventDefault();
      return false;
    }
  }


  /**
   * Triggered when submit button clicked
   * @param formData
   */
  onSubmit() {
    if (this.formGroup.valid) {
      const formData = this.formGroup.value;
      const dataToSend = {
        ticketId: (JSON.stringify(this.ticketId)),
        status: (this.selectedStatus),
        category: (this.selectedDialogCategory),
        comment: (formData.comments),
      };
      this.spinner.show();
      this.supportService.getUpdateTicket(dataToSend).subscribe({
        next: (ticketResponse: any) => {
          try {
            // First, check if response is a string and parse it if needed
            let parsedResponse = ticketResponse;
            if (typeof ticketResponse === 'string') {
              parsedResponse = JSON.parse(ticketResponse);
            }

            // Check if response has encryptedBody property
            if (parsedResponse && parsedResponse.encryptedBody) {
              // Decrypt the encryptedBody
              const decrypted = this.utility.decrypt(parsedResponse.encryptedBody);
              parsedResponse = JSON.parse(decrypted);
            } else {
              // Try to decrypt the entire response if needed
              if (typeof ticketResponse === 'string') {
                const decrypted = this.utility.decrypt(ticketResponse);
                parsedResponse = JSON.parse(decrypted);
              }
            }

            this.toastr.success(parsedResponse.message);
            this.onCloseForm();
            if (this.isNewTicket) {
              this.isNewTicket = true;
              this.isprogressTicket = false;
              this.processingTickets();
            } else {
              this.isNewTicket = false;
              this.isprogressTicket = true;
              this.progressTickets();
            }
            this.setTableHeader();
          } catch (error) {
            console.error('Error parsing ticket response:', error);
            this.toastr.error('An error occurred while updating the ticket');
          }
          this.spinner.hide();
        },
        error: (errorResponse: any) => {
          try {
            let error = typeof errorResponse.error === 'string' ?
              JSON.parse(this.utility.decrypt(errorResponse.error)) :
              errorResponse.error;

            if (typeof error === 'string') {
              error = JSON.parse(error);
            }

            if (error && error.message) {
              this.toastr.error(error.message);
            } else {
              this.toastr.error('An error occurred while updating the ticket');
            }

            let errorMsg = errorResponse.status;
            if (+errorMsg === 401 || +errorMsg === 404) {
              localStorage.clear();
              this.router.navigate(['']);
              this.toastr.success('Signed Out Successfully');
            }
          } catch (e) {
            console.error('Error parsing error response:', e);
            this.toastr.error('An error occurred');
          }
          this.spinner.hide();
        },
      });
    } else {
      // Mark all form controls as touched to trigger validation messages
      Object.keys(this.formGroup.controls).forEach(key => {
        const control = this.formGroup.get(key);
        control?.markAsTouched();
      });
    }
  }

  showTooltipIfTruncated(event: MouseEvent): void {
    const inputElement = event.target as HTMLInputElement;
    if (inputElement && inputElement.scrollWidth > inputElement.clientWidth) {
      inputElement.title = inputElement.value;
    } else {
      inputElement.removeAttribute("title");
    }
  }

  funRestSearchPrevent(event: any) {
    if (event.target.selectionStart === 0 && event.code === 'Space') {
      event.preventDefault();
    }
    if (event.target.value.substr(-1) === ' ' && event.code === 'Space') {
      event.preventDefault();
    }

    var k = event.charCode || event.keyCode;
    if (
      (k >= 65 && k <= 90) ||
      (k >= 97 && k <= 122) ||
      k == 8 ||
      k == 32 ||
      (k >= 48 && k <= 57)
    ) {
      // Allow uppercase letters, lowercase letters, backspace, space, and numbers
    } else {
      event.preventDefault();
    }

    return (
      (k >= 65 && k <= 90) ||
      (k >= 97 && k <= 122) ||
      k == 8 ||
      k == 32 ||
      (k >= 48 && k <= 57)
    );
  }

  cancelApprove() {
    this.approvePromotionDialogRef.close();
  }

  cancelReject() {
    this.rejectPromotionDialogRef.close();
  }

  submitApprove() {
    // if (!this.selectedPromotionData) {
    //   this.toastr.warning('Please select a promotion to approve');
    //   return;
    // }

    // this.spinner.show();
    // this.supportService.approvePromotion(this.selectedPromotionData.id).subscribe({
    //   next: (response: any) => {
    //     this._spinner.hide();
    //     this.toastr.success('Promotion approved successfully');
    //     this.approvePromotionDialogRef.close();
    //     this.getAllPromotions();
    //   },
    //   error: (errorResponse: any) => {
    //     this.spinner.hide();
    //     let error: any = (errorResponse.error);
    //     try {
    //       error = JSON.parse(error);
    //       this.toastr.error(error.message || 'Failed to approve promotion');
    //     } catch (e) {
    //       this.toastr.error('Failed to approve promotion');
    //     }
    //   }
    // });
  }

  submitReject() {   
    // if (!this.selectedPromotionData) {
    //   this.toastr.warning('Please select a promotion to reject');
    //   return;
    // }

    // if (!this.rejectComment.trim()) {
    //   this.toastr.warning('Please enter a comment');
    //   return;
    // }

    // this.spinner.show();
    // this.supportService.rejectPromotion(this.selectedPromotionData.id, this.rejectComment).subscribe({
    //   next: (response: any) => {
    //     this._spinner.hide(); 
    //     this.toastr.success('Promotion rejected successfully');
    //     this.rejectPromotionDialogRef.close();
    //     this.getAllPromotions();
    //   },
    //   error: (errorResponse: any) => {
    //     this._spinner.hide();
    //     let error: any = (errorResponse.error);
    //     try {
    //       error = JSON.parse(error);
    //       this.toastr.error(error.message || 'Failed to reject promotion');
    //     } catch (e) {
    //       this.toastr.error('Failed to reject promotion');
    //     }
    //   }
    // });
  }

  addMoreProductRow() {
    this.productRows.push({
      selectedProduct: [],
      quantity: null,
      productId: null
    });
  }

  // Load all products independently (not dependent on region/zone)
  loadAllProducts() {
    this.spinner.show();
    // Call the existing API to get all products - this API gets all products regardless of the parameter
    this.rewardPointService.getProductByID({}).subscribe({
      next: (response: any) => {
        const parsedData = typeof response === 'string' ? JSON.parse(this.utility.decryptString(response)) : JSON.parse(response);

        if (Array.isArray(parsedData)) {
          this.DataList = parsedData.map((item: any) => ({
            id: item.id || item.databricks_material_cd,
            itemName: item.databricks_material_desc || item.description,
            materialCode: item.databricks_material_cd || '',
            bonusPercentage: item.bonusPercentage || 0,
            materialNumber: item.databricks_material_nbr,
            category: item.category || '',
            portfolio: item.portfolio || '',
          }));

          // Update productDataList for multiselect dropdown
          this.productDataList = this.DataList.map((item: any) => ({
            id: item.id,
            itemName: item.itemName
          }));
        }
        this.spinner.hide();
      },
      error: (error: any) => {
        console.error('Failed to load products:', error);
        // Fallback: keep productDataList empty if API fails
        this.productDataList = [];
        this.spinner.hide();
      }
    });
  }

  // Reset promotion dialog data
  resetPromotionDialog() {
    this.productRows = [];
    this.selectedRegion = [];
    this.selectedZone = [];
    this.selectedProduct = [];
    this.additionalProducts = [];
    this.selectedFileName = '';
  }



  approvePromotion(data: any) {
    this.selectedPromotionData = data;
    this.approvePromotionDialogRef = this.dialog.open(this.approvePromotionTemplate, {
      width: '400px',
      disableClose: false,
      panelClass: 'confirm-dialog-container',
      hasBackdrop: true,
      autoFocus: false,
      restoreFocus: true,
    });
  }

  rejectPromotion(data: any) {
    this.selectedPromotionData = data;
    this.rejectPromotionDialogRef = this.dialog.open(this.rejectPromotionTemplate, {
      width: '400px',
      disableClose: false,
      panelClass: 'confirm-dialog-container',
      hasBackdrop: true,
      autoFocus: false,
      restoreFocus: true,
    });
  }


}

