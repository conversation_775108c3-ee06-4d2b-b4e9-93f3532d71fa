import { Component, OnInit, TemplateRef, ViewChild, ViewEncapsulation } from '@angular/core';
import { FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { MatDialog, MatDialogModule, MatDialogRef } from '@angular/material/dialog';
import { PageEvent } from '@angular/material/paginator';
import { Router, RouterModule } from '@angular/router';
import { ngxCsv } from 'ngx-csv';
import { ToastrService } from 'ngx-toastr';
import { Subject, debounceTime } from 'rxjs';
import { RewardPointsService } from 'src/app/app-services/reward-points-service';
import { SidebarServiceService } from 'src/app/app-services/sidebar-service.service';
import { SupportService } from 'src/app/app-services/support.service';
import { AppConstant } from 'src/app/constants/app.constant';
import { GlobalEvents } from 'src/app/helpers/global.events';
import { Utility } from "src/app/shared/utility/utility";
import { BaThemeSpinner } from 'src/app/theme/services';
// import * as saveAs from 'file-saver';
import { CommonModule } from '@angular/common';
// import { AllSupportRoutingModule } from './all-support-routing.module';
import { MatButtonToggleModule } from '@angular/material/button-toggle';
// import { AllSupportComponent } from './all-support.component';
import { DynamicTableComponent } from 'src/app/shared/data-table/data-table.component';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { AngularMultiSelectModule } from 'angular2-multiselect-dropdown';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { MatButtonModule } from '@angular/material/button';
import { MatSelectModule } from '@angular/material/select';
import { MatInputModule } from '@angular/material/input';
import { MatIconModule } from '@angular/material/icon';
import { MatPaginatorModule } from '@angular/material/paginator';
import { CustomCasePipe } from "src/app/app-services/custom-case.pipe";
import { UserService } from 'src/app/app-services/user-service';
import { AuthenticationHelper } from 'src/app/helpers/authentication';
import { RegionsService } from 'src/app/app-services/regions-service';
import { ZonesService } from 'src/app/app-services/zones-service';
import { DashboardService } from 'src/app/app-services/dashboard.service';
import { promotionsService } from 'src/app/app-services/promotions.service';
import moment from 'moment';

interface ConfigurationSettings {
  showPagination: boolean;
  perPage: number;
  totalRecordCount: number;
  currentPage: number;
  showActionsColumn: boolean;
  actionsColumnName: string;
  productIcon: boolean;
  noDataMessage: string;
  showEdit: boolean;
  showDelete?: boolean;
  changeStatus: boolean;
}

export enum ToggleEnum {
  Option1,
  Option2,
}
@Component({
  selector: 'app-all-promotions',
  templateUrl: './promotions.html',
  styleUrls: ['./promotions.scss'],
  encapsulation: ViewEncapsulation.None, // This is crucial for production builds
  imports: [
    CommonModule,
    RouterModule,
    FormsModule,
    ReactiveFormsModule,
    MatButtonToggleModule,
    MatDatepickerModule,
    MatNativeDateModule,
    MatButtonModule,
    MatSelectModule,
    MatInputModule,
    MatIconModule,
    MatPaginatorModule,
    MatDialogModule,
    AngularMultiSelectModule,
    DynamicTableComponent,
  ]
})

export class AllPromotionsComponent implements OnInit {
  viewProductsDialogRef!: MatDialogRef<any>;
  @ViewChild('viewProductsTemplate') viewProductsTemplate!: TemplateRef<any>;
  @ViewChild('supoortDialog') supoortDialog: TemplateRef<any> | undefined;
  @ViewChild('addEditPromotionTemplate') addEditPromotionTemplate!: TemplateRef<any>;
  @ViewChild('approvePromotionTemplate') approvePromotionTemplate!: TemplateRef<any>;
  @ViewChild('rejectPromotionTemplate') rejectPromotionTemplate!: TemplateRef<any>;
  supoortDialogRef!: MatDialogRef<any>;
  ismobileViewAllBrands: boolean = false;
  formGroup: FormGroup = new FormGroup({
    comments: new FormControl('', Validators.required)
  });

  dataTable: boolean = false;
  isView: boolean = false;
  isViewFalse: boolean = false;
  newFlag: boolean = false;
  progressFlag: boolean = false;
  userData: any = [];
  tableHead: any = [];
  tableColName: any = [];
  tableData: any = [];
  exportData: any = [];
  modelChanged: Subject<string> = new Subject<string>();
  showIndex: any = { index: null };

  configurationSettings: ConfigurationSettings = {
    showPagination: true,
    perPage: AppConstant.PER_PAGE_ITEMS,
    totalRecordCount: 0,
    currentPage: 1,
    showActionsColumn: true,
    actionsColumnName: 'Action',
    productIcon: true,
    noDataMessage: 'No data found',
    // showStatus: false,
    showEdit: true,
    showDelete: true, // Enable reject functionality (reusing showDelete for reject)
    changeStatus: false,
    // showIndex: true,
  };

  supportTab: string = 'PROMOTIONS';
  selectedStatus: string = '';
  activeButton: string | undefined;
  selectedDate: string | undefined;
  selectedValue: string = '1';
  isNewTicket: boolean = true;
  isprogressTicket: boolean = false;
  model: string = '';
  searchedValue: string | undefined;
  startDate: any;
  endDate: any;

  ticketNumber: string | undefined;
  queryTitle: string | undefined;
  createdDate: Date | undefined;

  totalRecordCount = 0;
  perPage = AppConstant.PER_PAGE_ITEMS; // Use the same constant as configurationSettings
  currentPage = 0;

  categoryDataList: any = [];
  productRows: any[] = [];
  dialogCategoryDataList: any = [];
  category: any = [];
  dialogCategory: any = [];
  statusDataList: any = [];
  status: any = [];
  ticketId: number | undefined;

  selectedCategory: string = '';
  selectedDialogCategory: string = '';

  categoryDropdownSettings = {
    text: 'Select Category',
    enableSearchFilter: true,
    classes: 'myclass customer-dd ',
    labelKey: 'name',
    enableFilterSelectAll: false,
    singleSelection: true,
    maxHeight: 200,
    disabled: false,
    autoPosition: false,
    badgeShowLimit: 1,
    showCheckbox: false,
  };

  dialogCategoryDropdownSettings = {
    text: 'Select Category',
    enableSearchFilter: true,
    classes: 'myclass customer-dd ',
    labelKey: 'name',
    enableFilterSelectAll: false,
    singleSelection: true,
    maxHeight: 200,
    disabled: false,
    autoPosition: false,
    badgeShowLimit: 1,
    showCheckbox: false,
  };

  statusDropdownSettings = {
    text: 'Select Status',
    enableSearchFilter: true,
    classes: 'myclass customer-dd ',
    labelKey: 'name',
    enableFilterSelectAll: false,
    singleSelection: true,
    maxHeight: 200,
    disabled: false,
    autoPosition: false,
    badgeShowLimit: 1,
    showCheckbox: false,
  };

  supportAttachments: any = [];
  userRole: string | null | undefined;

  // Product view popup properties
  viewProductHead: any = [];
  viewProductData: any = [];
  viewProductColName: any = [];

  previewConfigurationSettings: ConfigurationSettings = {
    showPagination: false,
    perPage: 0,
    totalRecordCount: 0,
    currentPage: 1,
    showActionsColumn: false,
    actionsColumnName: '',
    productIcon: false,
    noDataMessage: 'No products found',
    showEdit: false,
    changeStatus: false,
  };


  // Add/Edit promotion popup properties
  promotionForm !: FormGroup;
  isEditMode: boolean = false;
  editingPromotionId: number | null = null;
  selectedFileName: string = '';
  selectedFile: File | null = null;
  uploadedImageUrl: string = '';
  uploadedImageId: number | null = null;
  isImageUploading: boolean = false;
  additionalProducts: any[] = [];
  addEditPromotionDialogRef!: MatDialogRef<any>;

  // Multiselect data
  selectedRegion: any[] = [];
  selectedZone: any[] = [];
  selectedProduct: any[] = [];

  regionDataList = [];

  zoneDataList: any[] = [];

  productDataList: any[] = [];
  DataList: any[] = []; // For storing full product data

  regionDropdownSettings = {
    singleSelection: true,
    enableSearchFilter: true,
    classes: "multiselect-custom",
    badgeShowLimit: 1,
    searchPlaceholderText: 'Search Region',
    maxHeight: 150,
    disabled: false,
    autoPosition: false,
    showCheckbox: false
  };

  zoneDropdownSettings = {
    singleSelection: true,
    text: "Select Zone",
    selectAllText: 'Select All',
    unSelectAllText: 'UnSelect All',
    enableSearchFilter: true,
    classes: "multiselect-custom",
    badgeShowLimit: 1,
    searchPlaceholderText: 'Search Zone',
    maxHeight: 150,
    disabled: false,
    autoPosition: false,
    showCheckbox: false
  };

  productDropdownSettings = {
    singleSelection: true,
    text: "Select Product",
    selectAllText: 'Select All',
    unSelectAllText: 'UnSelect All',
    enableSearchFilter: true,
    classes: "multiselect-custom",
    badgeShowLimit: 1,
    searchPlaceholderText: 'Search Product',
    maxHeight: 130,
    disabled: false,
    autoPosition: false,
    showCheckbox: false
  };

  // Approve/Reject promotion properties
  selectedPromotionData: any = null;
  rejectComment: string = '';
  approvePromotionDialogRef!: MatDialogRef<any>;
  rejectPromotionDialogRef!: MatDialogRef<any>;

  constructor(
    private spinner: BaThemeSpinner,
    private supportService: SupportService,
    private toastr: ToastrService,
    private router: Router,
    private events: GlobalEvents,
    private utility: Utility,
    public dialog: MatDialog,
    private userService: UserService,
    private rewardPointService: RewardPointsService,
    private sidebarService: SidebarServiceService,
    private formBuilder: FormBuilder,
    private regionsService: RegionsService,
    private zonesService: ZonesService,
    private dashboardService: DashboardService,
    private promotionsService: promotionsService
  ) {
    this.ismobileViewAllBrands = window.innerWidth <= 1023 ? true : false;
    this.initializePromotionForm();
  }

  ngOnInit() {
    this.spinner.show();
    this.initializePromotionForm();
    this.getRegion(); // Load regions on component init
    this.loadAllProducts(); // Load all products independently on init
    const localRole = AuthenticationHelper.getRole()?.trim().toUpperCase();
    if (localRole) {
      this.setRoleBasedConfig(localRole);
      this.spinner.hide();
    }
    this.userService.userRole$.subscribe(apiRole => {
      const apiRoleFormatted = apiRole?.trim().toUpperCase();
      this.userRole = apiRoleFormatted;
      if (apiRoleFormatted && apiRoleFormatted !== localRole) {
        this.setRoleBasedConfig(apiRoleFormatted);
        this.spinner.hide();
      }
    });
    this.setTableHeader();
    this.modelChanged.pipe(debounceTime(500)).subscribe((model: string) => {
      this.searchedValue = model?.trim() || '';
      this.currentPage = 0;
      this.configurationSettings.currentPage = 1;
      this.setTableHeader();
    });
  }
  private setRoleBasedConfig(role: string) {
    if (role === 'VIEWER') {
      this.configurationSettings = {
        ...this.configurationSettings,
        showActionsColumn: false
      };
      setTimeout(() => {
        this.configurationSettings = { ...this.configurationSettings };
        this.tableData = [...(this.tableData || [])];
        this.setTableHeader();
      }, 100);
    }
    else {
      this.configurationSettings = {
        ...this.configurationSettings,
        showActionsColumn: true
      };
    }
  }


  toggleEnum = ToggleEnum;
  selectedState = ToggleEnum.Option1;
  onChange($event: any) {
    this.selectedState = $event.value;
  }

  onPageChange(event: PageEvent) {
    this.currentPage = event.pageIndex;
    this.perPage = event.pageSize;
    if (this.isNewTicket) {
      this.getPromotionData();
    } else if (this.isprogressTicket) {
      this.progressTickets();
    }
  }
  /**
   * Method for routing to edit or add user
   * @param event
   */
  id: any;
  title: any;
  description: string | undefined;

  userDetails(event: any) {
    let data = {
      id: event.id,
      ticketNumber: event.ticketNumber,
      title: event.title,
      description: event.description,
      comment: event.comment,
    };
    this.supoortDialogRef = this.dialog.open(this.supoortDialog!, {
      width: '70%',
      disableClose: false,
      panelClass: 'confirm-dialog-container',
      data,
      hasBackdrop: true,
    });
  }


  initializePromotionForm() {
    this.promotionForm = this.formBuilder.group({
      promotionName: ['', Validators.required],
      startDate: ['', Validators.required],
      endDate: ['', Validators.required],
      distributorName: ['', Validators.required],
      points: ['', [Validators.required, Validators.min(1)]],
      quantity: ['', [Validators.required, Validators.min(1)]]

    });
  }

  // Multiselect event handlers
  onRegionSelect(item: any): void {
    this.selectedRegion = [item];
    // Load zones for the selected region
    this.getZoneByID(item.id);
    // Reset zone selection when region changes
    this.selectedZone = [];
    // Note: Product data is now independent and loaded separately
  }

  onRegionDeSelect(item: any): void {
    this.selectedRegion = [];
    this.selectedZone = [];
    this.zoneDataList = []; // Clear zone data when region is deselected
    // Note: Product data remains available as it's independent
  }

  onZoneSelect(item: any): void {
    this.selectedZone = [item];
  }

  onZoneDeSelect(item: any): void {
    this.selectedZone = [];
  }

  onProductSelect(item: any): void {
    this.selectedProduct = [item];
  }

  onProductDeSelect(item: any): void {
    this.selectedProduct = [];
  }

  onAdditionalProductSelect(item: any, index: number): void {
    this.additionalProducts[index].selectedProduct = [item];
    this.additionalProducts[index].productId = item.id;
  }

  onAdditionalProductDeSelect(item: any, index: number): void {
    this.additionalProducts[index].selectedProduct = [];
    this.additionalProducts[index].productId = null;
  }

  // API Methods for loading regions and zones
  getRegion() {
    this.regionDataList = [];
    this.userService.getRegion().subscribe(
      (response: any) => {
        response = this.utility.decryptString(response)
        let leaderArray = JSON.parse(response);
        this.regionDataList = leaderArray.map((item: any) => ({
          id: item.id,
          itemName: item.name,
          code: item.code,
        }));
        console.log('Regions loaded:', this.regionDataList);
      },
      (error) => {
        console.error('Error loading regions:', error);
      }
    );
  }

  getZoneByID(id: any) {
    console.log('Loading zones for region ID:', id);
    const data = {
      zoneId: id
    }; // if needed, add request params here
    this.zoneDataList = [];
    this.userService.getZoneById(data).subscribe(
      (response: any) => {
        response = this.utility.decryptString(response)
        let leaderArray = JSON.parse(response);
        this.zoneDataList = leaderArray.map((item: any) => ({
          id: item.id,
          itemName: item.name,
          code: item.code,
        }));
        console.log('Zones loaded for region', id, ':', this.zoneDataList);
      },
      (error) => {
        console.error('Error loading zones for region', id, ':', error);
      }
    );
  }

  getAllProductData(event: any) {
    this.spinner.show();
    this.rewardPointService.getProductByID({ id: event.id }).subscribe({
      next: (response: any) => {
        const parsedData = typeof response === 'string' ? JSON.parse(this.utility.decryptString(response)) : JSON.parse(response);

        if (Array.isArray(parsedData)) {
          this.DataList = parsedData.map((item: any) => ({
            id: item.id || item.databricks_material_cd,
            itemName: item.databricks_material_desc || item.description,
            materialCode: item.databricks_material_cd || '',
            bonusPercentage: item.bonusPercentage || 0,
            materialNumber: item.databricks_material_nbr,
            category: item.category || '',
            portfolio: item.portfolio || '',
          }));

          // Update productDataList for multiselect dropdown
          this.productDataList = this.DataList.map((item: any) => ({
            id: item.id,
            itemName: item.itemName
          }));

          this.setPreSelectedValues(); // Update preselections after data load
        }
        this.spinner.hide();
      },
      error: (error) => {
        this.toastr.error('Failed to load products');
        this.spinner.hide();
      }
    });
  }

  setPreSelectedValues() {
    // Method to handle pre-selected values if needed
    // This can be implemented based on your requirements
  }

  async openDialogForm(data: any) {
    // Open the edit promotion dialog instead of the old support dialog
    this.openEditPromotionDialog(data);
  }



  async waitForTicketInfo(data: any): Promise<void> {
    return new Promise<void>((resolve) => {
      this.setTicketInfo(data);
      resolve(); // Resolve the promise immediately since setTicketInfo doesn't return a promise
    });
  }

  setTicketInfo(ticketInfo: any) {
    this.supportAttachments = [];
    ticketInfo.supportAttachments.forEach((supportAttachment: any) => {
      let supportAttachmentObject = {
        id: supportAttachment.id,
        url: supportAttachment.url,
        fileName: supportAttachment.fileName,
        contentType: supportAttachment.contentType,
        fileSize: supportAttachment.fileSize,
        supportMediaType: supportAttachment.supportMediaType,
      };
      this.supportAttachments.push(supportAttachmentObject);
    });
    this.dialogCategory = [];
    this.status = [];
    this.dialogCategoryDataList.forEach((categoryData: any) => {
      if (categoryData.name == ticketInfo.category) {
        this.dialogCategory.push(categoryData);
        this.selectedDialogCategory = categoryData.name;
      }
    });
    this.statusDataList.forEach((statusData: any) => {
      if (statusData.status == ticketInfo.status) {
        this.status.push(statusData);
        this.selectedStatus = statusData.status;
      }
    });
    this.ticketId = ticketInfo.id;
    if (this.isNewTicket) {
      this.formGroup = this.formBuilder.group({
        ticketNumber: [ticketInfo.ticketNumber],
        queryTitle: [ticketInfo.title],
        leaderName: [ticketInfo.leaderName, Validators.required],
        createdDate: [new Date(ticketInfo.createdDate)],
        comments: [ticketInfo.comment, Validators.required],
        description: [ticketInfo.description, Validators.required],
      });
    } else if (this.isprogressTicket) {
      this.formGroup = this.formBuilder.group({
        ticketNumber: [ticketInfo.ticketNumber, Validators.required],
        queryTitle: [ticketInfo.title, Validators.required],
        leaderName: [ticketInfo.leaderName, Validators.required],
        createdDate: [new Date(ticketInfo.updatedDate), Validators.required],
        comments: [ticketInfo.comment, Validators.required],
        description: [ticketInfo.description, Validators.required],
        distributorName: [ticketInfo.distributorName, Validators.required],
        distributorCode: [ticketInfo.distributorCode, Validators.required],
      });
    } else {
      this.formGroup = this.formBuilder.group({
        ticketNumber: [ticketInfo.ticketNumber, Validators.required],
        queryTitle: [ticketInfo.title, Validators.required],
        leaderName: [ticketInfo.leaderName, Validators.required],
        createdDate: [new Date(ticketInfo.createdDate), Validators.required],
        comments: [ticketInfo.comment, Validators.required],
        description: [ticketInfo.description, Validators.required],
      });
    }
    this.formGroup.patchValue({
      comments: ''
    });
    this.formGroup.get('ticketNumber')?.disable();
    this.formGroup.get('queryTitle')?.disable();
    this.formGroup.get('description')?.disable();
  }

  /**
   * Triggered when category are selected
   * @param data
   */
  onCloseForm() {
    this.supoortDialogRef.close();
    this.formGroup.reset();
    this.selectedCategory = '';
    this.selectedDialogCategory = '';
    this.selectedStatus = '';
    this.categoryDataList = [];
    this.statusDataList = [];
    this.category = [];
    this.status = [];
    this.supportAttachments = [];
    this.formGroup.patchValue({
      comments: '',
    });
  }

  ChangeStatus(event: any) { }

  /**
   * Method for the page change event
   * @param page
   */
  getUsersPageData(page: any) {
    // this.currentPage = page;
    if (!this.newFlag) {
      this.progressTickets(this.currentPage);
    } else {
      this.getPromotionData(this.currentPage);
    }
  }

  getsupportPageData(page: number): void {
    this.configurationSettings.currentPage = page;
    this.currentPage = page - 1; // Convert to 0-based index for API
    if (this.isNewTicket) {
      this.getPromotionData();
    } else if (this.isprogressTicket) {
      this.progressTickets();
    }
  }

  /**
   * Method for setting the headers of the table for different customer types
   */
  setTableHeader() {
    if (this.isNewTicket) {
      this.tableHead = [
        'Promotion Name',
        'Products',
        'Distributor Name',
        'Region',
        'Zone',
        'Start Date',
        'End Date',
        'Points',
        'Status',
      ];
      this.tableColName = [
        'promotionName',
        'products',
        'distributorName',
        'region',
        'zone',
        'startDate',
        'endDate',
        'points',
        'status',
      ];
      this.tableData = [];
      this.getPromotionData();
    } else if (this.isprogressTicket) {
      this.tableHead = [
        'Promotion Name',
        'Leader Name',
        'Product Name',
        'Portfolio',
        'Category',
        'Total Quantity',
        'Achieved Quantity',
        'Bonification Amount',
        'Distributor Name',
        'Distributor Code',
      ];
      this.tableColName = [
        'promotionName',
        'leaderName',
        'productName',
        'portfolio',
        'category',
        'totalQuantity',
        'achievedQuantity',
        'bonificationAmount',
        'distributorName',
        'distributorCode',
      ];
      this.tableData = [];
      this.progressTickets();
    } else {
      this.tableHead = ['Leader Name', 'Ticket No', 'Mobile No', 'Category', 'Query Title', 'Created Date', 'Status'];
      this.tableColName = ['leaderName', 'ticketNumber', 'mobileNo', 'category', 'title', 'createdDate', 'status'];
      this.tableData = [];
      this.getPromotionData();
    }
  }

  onWindowResizeBrands(event: any) {
    this.ismobileViewAllBrands = window.innerWidth <= 1023 ? true : false;
  }

  /**
   * Method for getting results on the basis of search query
   * @param searchString
   */
  onSearch(event: any) {
    this.modelChanged.next(event);
  }

  /**
   * Method for clearing search query
   */
  clearSearch() {
    this.searchedValue = '';
    this.model = '';
    // Reset pagination when clearing search
    this.currentPage = 0;
    this.configurationSettings.currentPage = 1;
    this.setTableHeader();
  }

  /**
   * Method for exporting data in CSV format
   * @param event
   */
  onExport(event: any) {
    if (event) {
      this.getSupportExportData();
    }
  }


  ticketTab(tab: string) {
    this.spinner.show();
    const localRole = AuthenticationHelper.getRole()?.trim().toUpperCase();
    this.model = '';
    this.searchedValue = '';
    this.startDate = '';
    this.endDate = '';
    this.category = '';
    this.selectedCategory = '';
    // Reset pagination when switching tabs
    this.currentPage = 0;
    this.configurationSettings.currentPage = 1;

    switch (tab) {
      case 'PROMOTIONS':
        this.supportTab = 'PROMOTIONS';
        this.isNewTicket = true;
        this.isprogressTicket = false;
        if (localRole === 'VIEWER') {
          this.configurationSettings.showActionsColumn = false;
        } else {
          this.configurationSettings.showActionsColumn = true;
        }
        break;

      case 'PROGRESS':
        this.supportTab = 'PROGRESS';
        this.isNewTicket = false;
        this.isprogressTicket = true;
        this.configurationSettings.showActionsColumn = false;  // Always false for PROGRESS tab
        break;

      default:
        this.isNewTicket = true;
        this.isprogressTicket = false;
        if (localRole === 'VIEWER') {
          this.configurationSettings.showActionsColumn = false;
        } else {
          this.configurationSettings.showActionsColumn = true;
        }
        break;
    }
    this.configurationSettings = { ...this.configurationSettings };

    this.setTableHeader();
    this.spinner.hide();
  }
  viewProduct(event: any) {
    this.openViewProduct(event);
  }

  openViewProduct(event: any) {
    this.spinner.show(); // Show spinner before opening dialog

    // Use the promotion's product data from the event
    console.log('View products for promotion:', event);

    if (event && event.fullData && event.fullData.products) {
      this.setupProductViewData(event.fullData.products);
      this.openProductViewDialog();
    } else {
      this.toastr.error('No product data available for this promotion');
      this.spinner.hide();
    }
  }

  private openProductViewDialog() {
    this.viewProductsDialogRef = this.dialog.open(this.viewProductsTemplate, {
      width: '60%',
      disableClose: false,
      panelClass: 'custom-popup',
      hasBackdrop: true,
      autoFocus: false
    });

    this.viewProductsDialogRef.afterOpened().subscribe(() => {
      this.spinner.hide(); // Hide spinner after dialog opens
    });

    this.viewProductsDialogRef.backdropClick().subscribe(() => {
      this.viewProductsDialogRef.close();
    });

    this.viewProductsDialogRef.afterClosed().subscribe(() => {
      // Clean up data when dialog closes
      this.viewProductData = [];
    });
  }

  setupProductViewData(promotionProducts?: any[]) {
    // Set up table headers for product view
    this.viewProductHead = [
      'Product Name',
      'Material Code',
      'Portfolio',
      'Category',
      'Target Quantity',
    ];

    this.viewProductColName = [
      'productName',
      'materialCode',
      'portfolio',
      'category',
      'quantity',
    ];

    // Use promotion products if provided, otherwise use DataList
    if (promotionProducts && promotionProducts.length > 0) {
      this.viewProductData = promotionProducts.map((item: any) => ({
        productName: item.productName || 'N/A',
        materialCode: item.materialCode || 'N/A',
        portfolio: item.portfolio || 'N/A',
        category: item.category || 'N/A',
        quantity: item.quantity || 0
      }));
    } else if (this.DataList && this.DataList.length > 0) {
      this.viewProductData = this.DataList.map((item: any) => ({
        productName: item.itemName,
        materialCode: item.materialCode || 'N/A',
        portfolio: item.portfolio || 'N/A',
        category: item.category || 'N/A',
        quantity: 0 // Default quantity since this is view data
      }));
    } else {
      // If no data available, show empty array
      this.viewProductData = [];
    }

    this.previewConfigurationSettings.totalRecordCount = this.viewProductData.length;
  }


  closePopup() {
    if (this.viewProductsDialogRef) {
      this.viewProductsDialogRef.close();
    }
  }

  getPageData(page: number) {
    this.previewConfigurationSettings.currentPage = page;
    // In a real application, you would fetch data for the specific page
    // For now, we're using static dummy data
  }

  // Add/Edit Promotion Methods
  openAddPromotionDialog() {
    this.isEditMode = false;
    this.editingPromotionId = null;
    this.promotionForm.reset();
    this.selectedFileName = '';
    this.selectedFile = null;
    this.uploadedImageUrl = '';
    this.uploadedImageId = null;
    this.isImageUploading = false;

    // Reset selections
    this.selectedRegion = [];
    this.selectedZone = [];

    // Initialize productRows with at least one row
    this.productRows = [{
      selectedProduct: [],
      quantity: null,
      productId: null
    }];

    // Load all products independently (not dependent on region/zone)
    this.loadAllProducts();

    this.addEditPromotionDialogRef = this.dialog.open(this.addEditPromotionTemplate, {
      width: '60%',
      maxWidth: '600px',
      disableClose: false,
      panelClass: 'custom-popup',
      hasBackdrop: true,
      autoFocus: false
    });

    this.addEditPromotionDialogRef.afterClosed().subscribe(() => {
      // Clean up when dialog closes
      this.resetPromotionDialog();
    });
  }



  openEditPromotionDialog(data: any) {
    this.isEditMode = true;
    console.log('Edit promotion data:', data);
    console.log('Edit mode set to:', this.isEditMode);

    // Show your spinner
    this.spinner.show();

    // Reset form and clear previous selections
    this.promotionForm.reset();
    this.selectedRegion = [];
    this.selectedZone = [];
    this.productRows = [];
    this.editingPromotionId = null;

    // Clear image data
    this.selectedFileName = '';
    this.selectedFile = null;
    this.uploadedImageUrl = '';
    this.uploadedImageId = null;
    this.isImageUploading = false;

    // Load all required data first
    this.loadAllProducts();
    this.getRegion();

    // Wait for data to load, then populate and open dialog
    setTimeout(() => {
      this.populateFormForEdit(data);

      // Open dialog after data is loaded
      this.addEditPromotionDialogRef = this.dialog.open(this.addEditPromotionTemplate, {
        width: '60%',
        maxWidth: '600px',
        disableClose: false,
        panelClass: 'custom-popup',
        hasBackdrop: true,
        autoFocus: false
      });

      // Hide spinner after dialog opens
      this.spinner.hide();

      this.addEditPromotionDialogRef.afterClosed().subscribe(() => {
        // Clean up when dialog closes
        this.resetPromotionDialog();
      });
    }, 2500);
  }

  populateFormForEdit(data: any) {
    console.log('Populating form with data:', data);

    // Get the full promotion data
    const promotionData = data.fullData || data;

    // Store the promotion ID for edit operations
    this.editingPromotionId = promotionData.id || null;

    // Populate basic form fields
    this.promotionForm.patchValue({
      promotionName: promotionData.promotionName || '',
      startDate: promotionData.startDate || '',
      endDate: promotionData.endDate || '',
      distributorName: promotionData.distributorName || '',
      points: promotionData.points || ''
    });

    // Populate region selection
    if (promotionData.region && this.regionDataList.length > 0) {
      const regionMatch: any = this.regionDataList.find((r: any) => r.id === promotionData.region.id);
      if (regionMatch) {
        this.selectedRegion = [regionMatch];
        // Load zones for this region
        this.getZoneByID(regionMatch.id);

        // Wait for zones to load, then select zone
        setTimeout(() => {
          if (promotionData.zone && this.zoneDataList.length > 0) {
            const zoneMatch = this.zoneDataList.find((z: any) => z.id === promotionData.zone.id);
            if (zoneMatch) {
              this.selectedZone = [zoneMatch];
            }
          }
        }, 500);
      }
    }

    // Populate product rows - wait for products to be loaded
    setTimeout(() => {
      if (promotionData.products && promotionData.products.length > 0) {
        console.log('Available products in productDataList:', this.productDataList);
        console.log('Products to match:', promotionData.products);

        this.productRows = promotionData.products.map((product: any) => {
          // Try multiple matching strategies
          let productMatch = this.productDataList.find((p: any) =>
            p.itemName === product.productName ||
            p.id === product.id ||
            p.itemName?.toLowerCase() === product.productName?.toLowerCase()
          );

          // If no match found, create a temporary product object for display
          if (!productMatch && product.productName) {
            productMatch = {
              id: product.id || `temp_${Date.now()}`,
              itemName: product.productName
            };
          }

          console.log(`Product "${product.productName}" matched with:`, productMatch);

          return {
            selectedProduct: productMatch ? [productMatch] : [],
            quantity: product.quantity || null,
            productId: product.id || null
          };
        });
      } else {
        // Initialize with at least one empty row
        this.productRows = [{
          selectedProduct: [],
          quantity: null,
          productId: null
        }];
      }

      console.log('Final productRows:', this.productRows);
    }, 1500); // Wait for products to load

    // Handle uploaded image if exists
    if (promotionData.promotionImages && promotionData.promotionImages.length > 0) {
      const firstImage = promotionData.promotionImages[0];
      this.uploadedImageId = firstImage.imageId || null;
      this.uploadedImageUrl = firstImage.imagePath || '';
      this.selectedFileName = firstImage.imageName || 'Existing Image';
    }

    console.log('Form populated - selectedRegion:', this.selectedRegion);
    console.log('Form populated - selectedZone:', this.selectedZone);
    console.log('Form populated - productRows:', this.productRows);
  }

  onFileSelect(event: any) {
    const file = event.target.files[0];
    if (file) {
      // Validate file type
      const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
      if (!allowedTypes.includes(file.type)) {
        this.toastr.error('Please select a valid image file (JPEG, PNG, GIF)');
        return;
      }

      // Validate file size (max 10MB)
      const maxSize = 10 * 1024 * 1024; // 10MB in bytes
      if (file.size > maxSize) {
        this.toastr.error('Image size should be less than 10 MB');
        return;
      }

      this.selectedFile = file;
      this.selectedFileName = file.name;
      this.uploadImage();
    }
  }

  uploadImage() {
    if (!this.selectedFile) return;

    this.isImageUploading = true;
    this.spinner.show();

    const formData = new FormData();
    // Changed from 'file' to 'image' to match backend expectation
    formData.append('image', this.selectedFile);

    this.promotionsService.uploadPromotionImages(formData).subscribe({
      next: (response: any) => {
        response = this.utility.decryptString(response);
        console.log('Decrypted response:', response);
        try {
          const parsedResponse = typeof response === 'string' ? JSON.parse(response) : response;

          // Store both image URL and ID
          this.uploadedImageUrl = parsedResponse.imageUrl || parsedResponse.url || parsedResponse.message || parsedResponse;
          this.uploadedImageId = parsedResponse.id || parsedResponse.imageId || null;

          console.log('Image upload response:', parsedResponse);
          console.log('Image ID:', this.uploadedImageId);

          this.toastr.success('Image uploaded successfully!');
        } catch (error) {
          console.error('Error parsing response:', error);

          // If parsing fails, try to extract ID from response
          this.uploadedImageUrl = response;
          console.log('Image URL:', this.uploadedImageUrl);
          // Try to extract ID if response is 
          // a simple number or contains ID
          if (typeof response === 'number') {

            this.uploadedImageId = response;
            response = this.utility.decryptString(response)
            console.log('Image ID:', this.uploadedImageId);
          } else if (typeof response === 'string' && !isNaN(Number(response))) {
            this.uploadedImageId = Number(response);
          }

          this.toastr.success('Image uploaded successfully!');
        }
        this.isImageUploading = false;
        this.spinner.hide();
      },
      error: (error: any) => {
        console.error('Image upload failed:', error);
        let errorMessage = 'Failed to upload image. Please try again.';

        // Provide more specific error messages if available
        if (error.error?.message) {
          errorMessage = error.error.message;
        } else if (error.status === 400) {
          errorMessage = 'Invalid request. Please check the file and try again.';
        } else if (error.status === 413) {
          errorMessage = 'File too large. Please select a smaller image.';
        }

        this.toastr.error(errorMessage);
        this.isImageUploading = false;
        this.selectedFile = null;
        this.selectedFileName = '';
        this.spinner.hide();
      }
    });
  }



  canAddMore(): boolean {
    // Check if the first product row has both product selected and quantity filled
    if (this.productRows.length > 0) {
      const firstRow = this.productRows[0];
      return !!(firstRow.selectedProduct.length && firstRow.quantity);
    }
    return false;
  }

  isFormDataValid(): boolean {
    // Check if required selections are made
    if (!this.selectedRegion.length || !this.selectedZone.length) {
      return false;
    }

    // Check if at least one product row has both product and quantity
    const hasValidProduct = this.productRows.some(row =>
      row.selectedProduct.length > 0 && row.quantity && row.quantity > 0
    );

    return hasValidProduct;
  }

  isSubmitEnabled(): boolean {
    // Check if form is valid
    if (!this.promotionForm.valid) {
      return false;
    }

    // Check if required selections are made
    if (!this.selectedRegion.length || !this.selectedZone.length) {
      return false;
    }

    // Check if at least one product row has both product and quantity
    const hasValidProduct = this.productRows.some(row =>
      row.selectedProduct.length > 0 && row.quantity && row.quantity > 0
    );

    if (!hasValidProduct) {
      return false;
    }

    // Check if image is uploading
    if (this.isImageUploading) {
      return false;
    }

    // For add mode, check if image is uploaded (optional for edit mode)
    if (!this.isEditMode && !this.uploadedImageId) {
      return false;
    }

    return true;
  }


  addMoreProduct(): void {
    if (this.canAddMore()) {
      this.additionalProducts.push({
        selectedProduct: [],
        quantity: null,
        productId: null
      });
    }
  }

  addProductRow(): void {
    // Add more product row functionality
    this.addMoreProductRow();
  }



onSubmitPromotion(): void {
  if (true) {
    // Prepare the API payload according to the required format
    let promotionPayload: any = {
      promotionName: this.promotionForm.get('promotionName')?.value,
      startDate: this.promotionForm.get('startDate')?.value
        ? moment(this.promotionForm.get('startDate')?.value).format('YYYY-MM-DD')
        : null,
      endDate: this.promotionForm.get('endDate')?.value
        ? moment(this.promotionForm.get('endDate')?.value).format('YYYY-MM-DD')
        : null,
      region: {
        id: this.selectedRegion[0].id
      },
      zone: {
        id: this.selectedZone[0].id
      },
      distributorName: this.promotionForm.get('distributorName')?.value,
      points: this.promotionForm.get('points')?.value,
      products: this.productRows
        .filter(row => row.selectedProduct.length && row.quantity)
        .map(row => {
          const selectedProduct = row.selectedProduct[0];
          const productData = this.DataList.find(p => p.id === selectedProduct.id);

          let productPayload: any = {
            productName: selectedProduct.itemName,
            materialCode: productData?.materialCode || '',
            portfolio: productData?.portfolio || '',
            category: productData?.category || '',
            quantity: row.quantity
          };

          // For edit mode, include the product ID if available
          if (this.isEditMode && row.productId) {
            productPayload.id = row.productId;
          }

          return productPayload;
        }),
      promotionImages: [
        {
          imageId: this.uploadedImageId
        }
      ]
    };

    // For edit mode, include the promotion ID
    if (this.isEditMode && this.editingPromotionId) {
      promotionPayload.id = this.editingPromotionId;
    }

    console.log('Promotion payload:', promotionPayload);

    // Call the appropriate API based on mode
    this.spinner.show();
    const apiCall = this.isEditMode
      ? this.promotionsService.updatePromotion(promotionPayload)
      : this.promotionsService.addPromotion(promotionPayload);

    apiCall.subscribe({
      next: (response: any) => {
        console.log('Promotion API response:', response);
        this.spinner.hide();
        this.closePromotionPopup();
        this.toastr.success(this.isEditMode ? 'Promotion updated successfully!' : 'Promotion added successfully!');
        this.getPromotionData();
      },
      error: (error: any) => {
        console.error('Failed to save promotion:', error);
        this.spinner.hide();

        let errorMessage = this.isEditMode
          ? 'Failed to update promotion. Please try again.'
          : 'Failed to create promotion. Please try again.';

        if (error.error?.message) {
          errorMessage = error.error.message;
        } else if (error.status === 400) {
          errorMessage = 'Invalid data. Please check all fields and try again.';
        }

        this.toastr.error(errorMessage);
      }
    });
  } else {
    // Mark all fields as touched to show validation errors
    Object.keys(this.promotionForm.controls).forEach(key => {
      this.promotionForm.get(key)?.markAsTouched();
    });
  }
}

  closePromotionPopup() {
    // Clear all form data when popup is closed
    this.clearFormData();
    if (this.addEditPromotionDialogRef) {
      this.addEditPromotionDialogRef.close();
    }
  }

  clearFormData(): void {
    // Reset form
    this.promotionForm.reset();

    // Clear multiselect data
    this.selectedRegion = [];
    this.selectedZone = [];
    this.selectedProduct = [];

    // Clear additional products and product rows
    this.additionalProducts = [];
    this.productRows = [];

    // Clear zone data list
    this.zoneDataList = [];

    // Clear product data lists
    this.productDataList = [];
    this.DataList = [];

    // Clear file selection and image upload data
    this.selectedFileName = '';
    this.selectedFile = null;
    this.uploadedImageUrl = '';
    this.uploadedImageId = null;
    this.isImageUploading = false;

    // Reset edit mode
    this.isEditMode = false;
  }


  getPromotionData(page?: any) {
    const VIEW_ICON_PATH = 'assets/img/product.svg';
    this.supportService.setButtonState('new');
    this.spinner.show();
    const currentPage = page !== undefined ? page : this.currentPage || 0;
    const pageSize = this.perPage || 20;

    let data = {
      searchedValue: this.searchedValue || '',
      unpaged: false,
      currentPage: currentPage,
      pageLimit: pageSize,
    };

    this.promotionsService.getPromotions(data).subscribe({
      next: (response: any) => {
        try {
          let parsedResponse = response;

          if (typeof response === 'string') {
            parsedResponse = JSON.parse(response);
          }

          // Check if response has encryptedBody property
          if (parsedResponse && parsedResponse.encryptedBody) {
            // Decrypt the encryptedBody
            const decrypted = this.utility.decrypt(parsedResponse.encryptedBody);
            parsedResponse = JSON.parse(decrypted);

          } else {
            // Try to decrypt the entire response
            const decrypted = this.utility.decrypt(response);
            parsedResponse = JSON.parse(decrypted);
          }

          if (parsedResponse && parsedResponse.content && parsedResponse.content.length) {
            this.tableData = [];
            let i = 1;
            parsedResponse.content.forEach((promotionInfo: any) => {
              console.log('Processing promotion info:', promotionInfo);
              this.showIndex = i++;

              // Format products display
              let productsDisplay = 'No Products';
              if (promotionInfo.products && promotionInfo.products.length > 0) {
                productsDisplay = promotionInfo.products.map((p: any) => p.productName).join(', ');
                if (productsDisplay.length > 50) {
                  productsDisplay = productsDisplay.substring(0, 50) + '...';
                }
              }

              let promotionObj = {
                id: promotionInfo.id || 'NA',
                promotionName: promotionInfo.promotionName || 'NA',
                products: VIEW_ICON_PATH, // Keep it simple for the dynamic table
                distributorName: promotionInfo.distributorName || 'NA',
                region: promotionInfo.region?.name || 'NA',
                zone: promotionInfo.zone?.name || 'NA',
                startDate: promotionInfo.startDate || 'NA',
                endDate: promotionInfo.endDate || 'NA',
                points: promotionInfo.points || 0,
                status: promotionInfo.isActive ? 'Active' : 'Inactive',

                // Store full data for popups and actions
                fullData: promotionInfo
              };
              this.tableData.push(promotionObj);
            });
            this.configurationSettings.totalRecordCount = parsedResponse.totalElements;
          } else {
            this.configurationSettings.totalRecordCount = 0;
            this.tableData = [];
            this.newFlag = false;
          }
          localStorage.setItem('newFlag', JSON.stringify(this.newFlag));
          this.events.setChangedContentTopText(
            'Promotions (' + this.configurationSettings.totalRecordCount + ')'
          );
          this.newFlag = true;
          this.spinner.hide();
        } catch (error) {
          console.error('Error parsing processing tickets:', error);
          this.configurationSettings.totalRecordCount = 0;
          this.tableData = [];
          this.spinner.hide();
        }
      },
      error: (errorResponse: any) => {
        try {
          let error = typeof errorResponse.error === 'string' ?
            JSON.parse(this.utility.decrypt(errorResponse.error)) :
            errorResponse.error;

          if (typeof error === 'string') {
            error = JSON.parse(error);
          }

          if (error && error.message) {
            this.toastr.error(error.message);
          }

          let errorMsg = errorResponse.status;
          if (+errorMsg === 401 || +errorMsg === 404) {
            localStorage.clear();
            this.router.navigate(['']);
            this.toastr.success('Signed Out Successfully');
          }
        } catch (e) {
          console.error('Error parsing error response:', e);
          this.toastr.error('An error occurred');
        }
        this.spinner.hide();
      },
    });
  }

  /**
   * API call for progress tab
   * @param page
   */
  progressTickets(page?: any) {
    this.supportService.setButtonState('progress');
    this.selectedValue = '2';
    this.spinner.show();
    this.newFlag = false;

    // Ensure proper default values for progress API
    const currentPage = page !== undefined ? page : this.currentPage || 0;
    const pageSize = this.perPage || 1;

    let data = {
      unpaged: true,
      currentPage: currentPage,
      pageLimit: pageSize,
      sort: 'string'
    };

    console.log('Progress API call data:', data);

    this.promotionsService.getPromotionProgress(data).subscribe({
      next: (res: any) => {
        try {
          // Parse the response
          let parsedResponse = typeof res === 'string' ? JSON.parse(res) : res;

          // Try to decrypt if needed
          if (typeof res === 'string') {
            try {
              const decrypted = this.utility.decryptString(res);
              parsedResponse = JSON.parse(decrypted);
            } catch (decryptError) {
              console.log('No decryption needed or failed, using parsed response');
            }
          }

          console.log('Progress API response:', parsedResponse);

          if (parsedResponse && parsedResponse.content && parsedResponse.content.length) {
            this.tableData = [];
            let i = 1;
            parsedResponse.content.forEach((progressInfo: any) => {
              console.log('Processing progress info:', progressInfo);
              this.showIndex = i++;

              let progressObj = {
                id: progressInfo.id || 'NA',
                promotionName: progressInfo.promotionName || 'NA',
                products: 'assets/img/product.svg', // Show product icon
                distributorName: progressInfo.distributorName || 'NA',
                region: 'NA', // Not available in progress response
                zone: 'NA', // Not available in progress response
                startDate: 'NA', // Not available in progress response
                endDate: 'NA', // Not available in progress response
                points: 'NA', // Not available in progress response
                status: 'In Progress',

                // Progress specific fields
                leaderName: progressInfo.leaderName || 'NA',
                productName: progressInfo.productName || 'NA',
                portfolio: progressInfo.portfolio || 'NA',
                category: progressInfo.category || 'NA',
                totalQuantity: progressInfo.totalQuantity || 0,
                achievedQuantity: progressInfo.achievedQuantity || 0,
                bonificationAmount: progressInfo.bonificationAmount || 0,
                distributorCode: progressInfo.distributorCode || 'NA',

                // Store full data for popups and actions
                fullData: progressInfo
              };

              this.tableData.push(progressObj);
            });
            this.configurationSettings.totalRecordCount = parsedResponse.totalElements;
            this.spinner.hide();
          } else {
            this.configurationSettings.totalRecordCount = 0;
            this.tableData = [];
            this.progressFlag = false;
          }
          this.events.setChangedContentTopText(
            'Promotions (' + this.configurationSettings.totalRecordCount + ')'
          );
          this.progressFlag = false;
          this.spinner.hide();
        } catch (error) {
          console.error('Error parsing progress tickets:', error);
          this.configurationSettings.totalRecordCount = 0;
          this.tableData = [];
          this.spinner.hide();
        }
      },
      error: (errorResponse: any) => {
        try {
          let error = typeof errorResponse.error === 'string' ?
            JSON.parse(this.utility.decrypt(errorResponse.error)) :
            errorResponse.error;

          if (typeof error === 'string') {
            error = JSON.parse(error);
          }

          if (error && error.message) {
            this.toastr.error(error.message);
          }

          let errorMsg = errorResponse.status;
          if (+errorMsg === 401 || +errorMsg === 404) {
            localStorage.clear();
            this.router.navigate(['']);
            this.toastr.success('Signed Out Successfully');
          }
        } catch (e) {
          console.error('Error parsing error response:', e);
          this.toastr.error('An error occurred');
        }
        this.spinner.hide();
      },
    });
  }

  /**
   * API call for exporting data in CSV format
   */
  getSupportExportData() {
    window.scrollTo(0, 0);
    this.spinner.show();
    let data = {
      status: this.supportTab == 'PROMOTIONS' ? true : false,
      currentPage: this.currentPage ? this.currentPage : 0,
      pageSize: this.perPage,
      searchedValue: this.searchedValue ? this.searchedValue : '',
      category: this.selectedCategory ? this.selectedCategory : '',
      startDate: this.startDate ? this.startDate : '',
      endDate: this.endDate ? this.endDate : '',
      sort: 'updatedDate,desc',
      unPaged: true,
    };
    const exports = this.supportService.getAllSupportExportData(data);
    exports.subscribe({
      next: (exportSupportData: any) => {
        try {
          // First, check if response is a string and parse it if needed
          let parsedResponse = exportSupportData;
          if (typeof exportSupportData === 'string') {
            parsedResponse = JSON.parse(exportSupportData);
          }

          // Check if response has encryptedBody property
          if (parsedResponse && parsedResponse.encryptedBody) {
            // Decrypt the encryptedBody
            const decrypted = this.utility.decrypt(parsedResponse.encryptedBody);
            parsedResponse = JSON.parse(decrypted);
          } else {
            // Try to decrypt the entire response if needed
            if (typeof exportSupportData === 'string') {
              const decrypted = this.utility.decrypt(exportSupportData);
              parsedResponse = JSON.parse(decrypted);
            }
          }

          this.exportData = [];
          if (parsedResponse && parsedResponse.content && parsedResponse.content.length) {
            parsedResponse.content.forEach((exportSupportInfo: any) => {
              let exportObj = {
                leaderName: exportSupportInfo.customerName
                  ? this.utility.toUpperCaseUtil(exportSupportInfo.customerName)
                  : 'NA',
                ticketNumber: exportSupportInfo.ticketNumber
                  ? exportSupportInfo.ticketNumber
                  : 'NA',
                mobileNo: exportSupportInfo.mobileNo
                  ? exportSupportInfo.mobileNo
                  : 'NA',
                title: exportSupportInfo.title ? exportSupportInfo.title : 'NA',
                description: exportSupportInfo.description
                  ? exportSupportInfo.description
                  : 'NA',
                comment: exportSupportInfo.comment
                  ? exportSupportInfo.comment
                  : 'NA',
                category: exportSupportInfo.category
                  ? exportSupportInfo.category
                  : 'NA',
              };
              this.exportData.push(exportObj);
            });
            let options = {
              fieldSeparator: ',',
              quoteStrings: '"',
              decimalseparator: '.',
              showLabels: true,
              headers: [
                'Leader Name',
                'Ticket Number',
                'Mobile No',
                'Query Title',
                'Description',
                'Comments',
                'Category',
              ],
            };
            if (this.isNewTicket) {
              new ngxCsv(this.exportData, 'New Ticket', options);
            } else {
              new ngxCsv(this.exportData, 'Progress Ticket', options);
            }
          } else {
            this.toastr.warning('No data available');
          }
          this.spinner.hide();
        } catch (error) {
          console.error('Error parsing export data:', error);
          this.toastr.warning('No data available');
          this.spinner.hide();
        }
      },
      error: (errorResponse: any) => {
        try {
          let error = typeof errorResponse.error === 'string' ?
            JSON.parse(this.utility.decrypt(errorResponse.error)) :
            errorResponse.error;

          if (typeof error === 'string') {
            error = JSON.parse(error);
          }

          if (error && error.message) {
            this.toastr.error(error.message);
          }

          let errorMsg = errorResponse.status;
          if (+errorMsg === 401 || +errorMsg === 404) {
            localStorage.clear();
            this.router.navigate(['']);
            this.toastr.success('Signed Out Successfully');
          }
        } catch (e) {
          console.error('Error parsing error response:', e);
          this.toastr.error('An error occurred');
        }
        this.spinner.hide();
      },
    });
  }



  functionForName(event: any) {
    const k = event.charCode || event.keyCode;
    const lastChar = event.target.value.slice(-1);

    if (event.target.selectionStart === 0 && event.code === 'Space') {
      event.preventDefault();
    }

    if (lastChar === ' ' && event.code === 'Space') {
      event.preventDefault();
    }

    // Allow letters (uppercase and lowercase), numbers, spaces, and specified characters
    if (
      (k >= 65 && k <= 90) ||
      (k >= 97 && k <= 122) ||
      k === 32 || // space
      (k >= 48 && k <= 57) || // numbers
      k === 8 || // backspace
      k === 95 || // underscore
      k === 45 || // hyphen
      k === 58 || // colon
      k === 46 || // period
      k === 44 || // comma
      k === 63 || // question mark
      k === 34 || // double quote
      k === 39 || // single quote
      k === 40 || // open parenthesis
      k === 41 || // close parenthesis
      k === 91 || // open square bracket
      k === 93 || // close square bracket
      k === 38 || // ampersand
      k === 42 // asterisk
    ) {
      return true;
    } else {
      event.preventDefault();
      return false;
    }
  }


  /**
   * Triggered when submit button clicked
   * @param formData
   */
  onSubmit() {
    if (this.formGroup.valid) {
      const formData = this.formGroup.value;
      const dataToSend = {
        ticketId: (JSON.stringify(this.ticketId)),
        status: (this.selectedStatus),
        category: (this.selectedDialogCategory),
        comment: (formData.comments),
      };
      this.spinner.show();
      this.supportService.getUpdateTicket(dataToSend).subscribe({
        next: (ticketResponse: any) => {
          try {
            // First, check if response is a string and parse it if needed
            let parsedResponse = ticketResponse;
            if (typeof ticketResponse === 'string') {
              parsedResponse = JSON.parse(ticketResponse);
            }

            // Check if response has encryptedBody property
            if (parsedResponse && parsedResponse.encryptedBody) {
              // Decrypt the encryptedBody
              const decrypted = this.utility.decrypt(parsedResponse.encryptedBody);
              parsedResponse = JSON.parse(decrypted);
            } else {
              // Try to decrypt the entire response if needed
              if (typeof ticketResponse === 'string') {
                const decrypted = this.utility.decrypt(ticketResponse);
                parsedResponse = JSON.parse(decrypted);
              }
            }

            this.toastr.success(parsedResponse.message);
            this.onCloseForm();
            if (this.isNewTicket) {
              this.isNewTicket = true;
              this.isprogressTicket = false;
              this.getPromotionData();
            } else {
              this.isNewTicket = false;
              this.isprogressTicket = true;
              this.progressTickets();
            }
            this.setTableHeader();
          } catch (error) {
            console.error('Error parsing ticket response:', error);
            this.toastr.error('An error occurred while updating the ticket');
          }
          this.spinner.hide();
        },
        error: (errorResponse: any) => {
          try {
            let error = typeof errorResponse.error === 'string' ?
              JSON.parse(this.utility.decrypt(errorResponse.error)) :
              errorResponse.error;

            if (typeof error === 'string') {
              error = JSON.parse(error);
            }

            if (error && error.message) {
              this.toastr.error(error.message);
            } else {
              this.toastr.error('An error occurred while updating the ticket');
            }

            let errorMsg = errorResponse.status;
            if (+errorMsg === 401 || +errorMsg === 404) {
              localStorage.clear();
              this.router.navigate(['']);
              this.toastr.success('Signed Out Successfully');
            }
          } catch (e) {
            console.error('Error parsing error response:', e);
            this.toastr.error('An error occurred');
          }
          this.spinner.hide();
        },
      });
    } else {
      // Mark all form controls as touched to trigger validation messages
      Object.keys(this.formGroup.controls).forEach(key => {
        const control = this.formGroup.get(key);
        control?.markAsTouched();
      });
    }
  }

  showTooltipIfTruncated(event: MouseEvent): void {
    const inputElement = event.target as HTMLInputElement;
    if (inputElement && inputElement.scrollWidth > inputElement.clientWidth) {
      inputElement.title = inputElement.value;
    } else {
      inputElement.removeAttribute("title");
    }
  }

  funRestSearchPrevent(event: any) {
    if (event.target.selectionStart === 0 && event.code === 'Space') {
      event.preventDefault();
    }
    if (event.target.value.substr(-1) === ' ' && event.code === 'Space') {
      event.preventDefault();
    }

    var k = event.charCode || event.keyCode;
    if (
      (k >= 65 && k <= 90) ||
      (k >= 97 && k <= 122) ||
      k == 8 ||
      k == 32 ||
      (k >= 48 && k <= 57)
    ) {
      // Allow uppercase letters, lowercase letters, backspace, space, and numbers
    } else {
      event.preventDefault();
    }

    return (
      (k >= 65 && k <= 90) ||
      (k >= 97 && k <= 122) ||
      k == 8 ||
      k == 32 ||
      (k >= 48 && k <= 57)
    );
  }

  cancelApprove() {
    this.approvePromotionDialogRef.close();
  }

  cancelReject() {
    this.rejectPromotionDialogRef.close();
  }

  submitApprove() {
    // if (!this.selectedPromotionData) {
    //   this.toastr.warning('Please select a promotion to approve');
    //   return;
    // }

    // this.spinner.show();
    // this.supportService.approvePromotion(this.selectedPromotionData.id).subscribe({
    //   next: (response: any) => {
    //     this._spinner.hide();
    //     this.toastr.success('Promotion approved successfully');
    //     this.approvePromotionDialogRef.close();
    //     this.getAllPromotions();
    //   },
    //   error: (errorResponse: any) => {
    //     this.spinner.hide();
    //     let error: any = (errorResponse.error);
    //     try {
    //       error = JSON.parse(error);
    //       this.toastr.error(error.message || 'Failed to approve promotion');
    //     } catch (e) {
    //       this.toastr.error('Failed to approve promotion');
    //     }
    //   }
    // });
  }

  submitReject() {
    // if (!this.selectedPromotionData) {
    //   this.toastr.warning('Please select a promotion to reject');
    //   return;
    // }

    // if (!this.rejectComment.trim()) {
    //   this.toastr.warning('Please enter a comment');
    //   return;
    // }

    // this.spinner.show();
    // this.supportService.rejectPromotion(this.selectedPromotionData.id, this.rejectComment).subscribe({
    //   next: (response: any) => {
    //     this._spinner.hide(); 
    //     this.toastr.success('Promotion rejected successfully');
    //     this.rejectPromotionDialogRef.close();
    //     this.getAllPromotions();
    //   },
    //   error: (errorResponse: any) => {
    //     this._spinner.hide();
    //     let error: any = (errorResponse.error);
    //     try {
    //       error = JSON.parse(error);
    //       this.toastr.error(error.message || 'Failed to reject promotion');
    //     } catch (e) {
    //       this.toastr.error('Failed to reject promotion');
    //     }
    //   }
    // });
  }

  addMoreProductRow() {
    this.productRows.push({
      selectedProduct: [],
      quantity: null,
      productId: null
    });
  }

  // Load all products independently (not dependent on region/zone)
  loadAllProducts() {
    this.spinner.show();
    // Call the existing API to get all products - this API gets all products regardless of the parameter
    this.rewardPointService.getProductByID({}).subscribe({
      next: (response: any) => {
        const parsedData = typeof response === 'string' ? JSON.parse(this.utility.decryptString(response)) : JSON.parse(response);

        if (Array.isArray(parsedData)) {
          this.DataList = parsedData.map((item: any) => ({
            id: item.id || item.databricks_material_cd,
            itemName: item.databricks_material_desc || item.description,
            materialCode: item.databricks_material_cd || '',
            bonusPercentage: item.bonusPercentage || 0,
            materialNumber: item.databricks_material_nbr,
            category: item.category || '',
            portfolio: item.portfolio || '',
          }));

          // Update productDataList for multiselect dropdown
          this.productDataList = this.DataList.map((item: any) => ({
            id: item.id,
            itemName: item.itemName
          }));
        }
        this.spinner.hide();
      },
      error: (error: any) => {
        console.error('Failed to load products:', error);
        // Fallback: keep productDataList empty if API fails
        this.productDataList = [];
        this.spinner.hide();
      }
    });
  }

  // Reset promotion dialog data
  resetPromotionDialog() {
    this.productRows = [];
    this.selectedRegion = [];
    this.selectedZone = [];
    this.selectedProduct = [];
    this.additionalProducts = [];
    this.selectedFileName = '';
    this.selectedFile = null;
    this.uploadedImageUrl = '';
    this.uploadedImageId = null;
    this.isImageUploading = false;
    this.editingPromotionId = null;
    this.isEditMode = false;
  }



  approvePromotion(data: any) {
    this.selectedPromotionData = data;
    this.approvePromotionDialogRef = this.dialog.open(this.approvePromotionTemplate, {
      width: '400px',
      disableClose: false,
      panelClass: 'confirm-dialog-container',
      hasBackdrop: true,
      autoFocus: false,
      restoreFocus: true,
    });
  }

  rejectPromotion(data: any) {
    this.selectedPromotionData = data;
    this.rejectPromotionDialogRef = this.dialog.open(this.rejectPromotionTemplate, {
      width: '400px',
      disableClose: false,
      panelClass: 'confirm-dialog-container',
      hasBackdrop: true,
      autoFocus: false,
      restoreFocus: true,
    });
  }


}

