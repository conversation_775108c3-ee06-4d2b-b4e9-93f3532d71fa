<div class="modal-overlay" *ngIf="isOpen">
    <form class="modal-content target-modal" [formGroup]="targetForm" (ngSubmit)="handleSubmit()">
        <div class="modal-upper-section">
            <div class="modal-heading">{{ modalType === 'add' ? 'Add Target' : 'Edit Target' }}</div>
            <div class="form-section grower-section">
                <label class="checkbox-container">
                    <input type="checkbox" formControlName="grower">
                    <span class="checkmark"></span>
                    <span class="grower-text">Grower</span>
                </label>
            </div>

            <div class="form-section input-grid">
                <div class="form-group">
                    <label for="rootRfcId">Root RFC ID<span class="compulsory-field">*</span></label>
                    <input type="text" (keypress)="preventSpecialCharacters($event)"  id="rootRfcId" formControlName="rootRfcId" autocomplete="off" [class.invalid]="(targetForm.get('rootRfcId')?.touched || targetForm.get('rootRfcId')?.dirty) && 
                           targetForm.get('rootRfcId')?.invalid" (input)="transformToUppercase($event, 'rootRfcId')">
                    <div *ngIf="(targetForm.get('rootRfcId')?.touched || targetForm.get('rootRfcId')?.dirty) && 
                targetForm.get('rootRfcId')?.errors?.['required']" class="error-message">
                        RFC ID is required
                    </div>
                    <div *ngIf="(targetForm.get('rootRfcId')?.touched || targetForm.get('rootRfcId')?.dirty) && 
                targetForm.get('rootRfcId')?.errors?.['pattern']" class="error-message">
                        Invalid RFC format. Format: 3 or 4 letters + 6 digits + 3 alphanumeric (e.g. ABCD9512035A1)
                    </div>
                </div>

                <div class="form-group-row">
                    <span>
                        <label>Leader name<i class="required">*</i></label>
                        <angular2-multiselect *ngIf="!statusDropdownLeaderName.disabled" [data]="leaderList"
                            [(ngModel)]="selectedLeader" [settings]="statusDropdownLeaderName"
                            (onSelect)="onLeaderSelect($event)" (onDeSelect)="onLeaderDeselect($event)"
                            (onDeSelectAll)="onLeaderDeselectAll($event)" [ngModelOptions]="{ standalone: true }">
                        </angular2-multiselect>

                        <div *ngIf="statusDropdownLeaderName.disabled" class="disabled-leader-display">
                            {{ selectedLeader[0]?.itemName || 'NA' }}
                        </div>
                    </span>
                </div>
            </div>
            <div class="add-btn-container">
                <button type="button" 
                        class="addSchemeButton" 
                        (click)="addChildRfc()" 
                        [disabled]="isAddChildRfcDisabled()">
                    <img width="18px" src="assets/img/addButton.png" alt="Add Button" />
                    Add Child RFC
                </button>
            </div>

            <div *ngFor="let child of childRfcArray.controls; let i = index" class="form-group-child-rfc">
                <div class="child-rfc-row">
                    <!-- RFC ID Field -->
                    <div class="form-field additional-height">
                        <label>Child RFC ID</label>
                        <input type="text" 
                            [formControl]="$any(child.get('id'))" 
                            class="input-field"
                            (keypress)="preventSpecialCharacters($event)" 
                            (input)="transformToUppercase($event, 'childRfcId', i)" 
                            (blur)="child.get('id')?.markAsTouched()"
                            [class.invalid]="child.get('id')?.invalid && child.get('id')?.touched">
                        <div *ngIf="child.get('id')?.invalid && child.get('id')?.touched"
                            class="error-message">
                            <div *ngIf="child.get('id')?.errors?.['required']">RFC ID is required</div>
                            <div *ngIf="child.get('id')?.errors?.['pattern']">Invalid RFC format</div>
                        </div>
                    </div>

                    <!-- Company Name Field -->
                    <div class="form-field additional-height">
                        <label>Company name</label>
                        <input type="text" 
                            class="input-field" 
                            (keypress)="funRestName($event)" 
                            [formControl]="$any(child.get('companyname'))"
                            (blur)="child.get('companyname')?.markAsTouched()">
                        <div *ngIf="child.get('companyname')?.invalid && child.get('companyname')?.touched"
                            class="error-message">
                            <div *ngIf="child.get('companyname')?.errors?.['required']">Company name is required</div>
                        </div>
                    </div>

                    <!-- Tax Proof Field -->
                    <div class="form-field upload-container-image additional-height">
                        <label>Tax Proof</label>
                        <div class="file-input-wrapper">
                            <label class="file-input-label">
                                <input type="file" 
                                    #pdfInput 
                                    accept=".pdf" 
                                    (change)="onChildFileSelected($event, i)" 
                                    class="file-input">
                                <span class="file-input-text">
                                    {{ child.value.taxProof?.name || 'Upload PDF file...' }}
                                </span>
                            </label>
                        </div>
                        <div *ngIf="child.get('taxProof')?.invalid && child.get('taxProof')?.touched" 
                            class="error-message tax-proof">
                            Tax proof is required
                        </div>
                    </div>

                    <div class="form-field delete-container">
                        <button type="button" class="delete-btn" (click)="deleteChildRfc(i)" disabled="{{child.value.fromApi}}" [ngStyle]="{
                            opacity: child.value.fromApi ? '0.5' : '1',
                            cursor: child.value.fromApi ? 'not-allowed' : 'pointer'
                            }" >
                            <i class="fa fa-trash" aria-hidden="true"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <div class="modal-middle-section">
            <div class="modal-title">Set Target<span class="compulsory-bold-field">*</span></div>
            <div class="form-section set-target-section">
                <div class="target-values">
                    <div class="target-item">
                        <label>Total Target (Mex$)</label>
                        <input type="text" formControlName="totalTarget" (input)="onTotalTargetInput($event)"
                            (blur)="formatTotalTarget()" (keydown)="preventMinus($event)">
                    </div>
                    <div class="target-item" *ngIf="!growerChecked">
                        <label>CP &nbsp; (%)</label>
                        <div class="percentage-input">
                            <input type="number" formControlName="cpPercent">
                        </div>
                        <div class="amount-display">
                            <span>Amount (Mex$)</span>
                            <input type="text" formControlName="cpAmount"
                                [value]="targetForm.get('cpAmount')?.value ? formatUSD(targetForm.get('cpAmount')?.value) : formatUSD(targetForm.value.totalTarget * targetForm.value.cpPercent / 100)"
                                readonly>
                        </div>
                    </div>
                    <div class="target-item" *ngIf="!growerChecked">
                        <label>NPP &nbsp; (%)</label>
                        <div class="percentage-input">
                            <input type="number" formControlName="nppPercent" readonly>
                        </div>
                        <div class="amount-display">
                            <span>Amount (Mex$)</span>
                            <input type="text" formControlName="nppAmount"
                                [value]="targetForm.get('nppAmount')?.value ? formatUSD(targetForm.get('nppAmount')?.value) : formatUSD(targetForm.value.totalTarget * targetForm.value.nppPercent / 100)"
                                readonly>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="modal-bottom-section">
            <div class="form-section target-duration-section">
                <h3 class="modal-title">Target Duration<span class="compulsory-bold-field">*</span></h3>
                <div class="date-inputs">
                    <div class="date-group">
                        <label>Start Date</label>
                        <div class="date-picker">
                            <input [matDatepicker]="startPicker" formControlName="startDate" [min]="minDate"
                                [(ngModel)]="startDate" (dateChange)="onStartDateChange($event.value)"
                                (focus)="startPicker.open()">
                            <mat-datepicker-toggle [for]="startPicker">
                                <mat-icon>calendar_today</mat-icon>
                            </mat-datepicker-toggle>
                            <mat-datepicker #startPicker></mat-datepicker>
                        </div>
                    </div>
                    <div class="date-group">
                        <label>End Date</label>
                        <div class="date-picker">
                            <input [matDatepicker]="endPicker" formControlName="endDate"
                                (dateChange)="onEndDateChange($event.value)" [(ngModel)]="endDate"
                                [min]="targetForm.get('startDate')?.value" (focus)="endPicker.open()">
                            <mat-datepicker-toggle [for]="endPicker">
                                <mat-icon>calendar_today</mat-icon>
                            </mat-datepicker-toggle>
                            <mat-datepicker #endPicker></mat-datepicker>
                        </div>
                    </div>
                </div>

                <div *ngIf="targetForm.hasError('datesAreSame')" class="error-message">
                    Start date and End date cannot be the same.
                </div>

                <div *ngIf="targetForm.hasError('endDateBeforeStartDate')" class="error-message">
                    <span class="error-text">End date cannot be before start date!</span>
                </div>


                <div class="button-group">
                    <button type="button" class="btn cancel" (click)="handleClose()">Cancel</button>
                    <button type="submit" 
                            class="btn add" 
                            [disabled]="targetForm.invalid || hasEmptyChildRfcFields()">
                        Save
                    </button>
                </div>
            </div>
        </div>
    </form>
</div>